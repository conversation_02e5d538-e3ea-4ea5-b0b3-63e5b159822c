[1mdiff --git a/src/components/simulator/SimulatorResults.tsx b/src/components/simulator/SimulatorResults.tsx[m
[1mindex df80f1d..35bce99 100644[m
[1m--- a/src/components/simulator/SimulatorResults.tsx[m
[1m+++ b/src/components/simulator/SimulatorResults.tsx[m
[36m@@ -79,16 +79,17 @@[m [mconst SimulatorResults: React.FC<SimulatorResultsProps> = ({[m
 [m
   return ([m
     <div className="space-y-6">[m
[32m+[m[32m      {/* Move ResultViewToggle outside ResultsCard */}[m
[32m+[m[32m      <ResultViewToggle[m[41m [m
[32m+[m[32m        viewMode={resultViewMode}[m[41m [m
[32m+[m[32m        onViewModeChange={setResultViewMode}[m[41m [m
[32m+[m[32m      />[m
[32m+[m
       <ResultsCard[m
         debugMode={debugMode}[m
         cdrRate={results.cdrRate}[m
         cdrValue={results.cdrValue}[m
       >[m
[31m-        <ResultViewToggle [m
[31m-          viewMode={resultViewMode} [m
[31m-          onViewModeChange={setResultViewMode} [m
[31m-        />[m
[31m-[m
         <AnimatePresence>[m
           {debugMode && ([m
             <InvestmentDebugInfo[m
