
import { getCDRRate } from "../data/CDRBreakpoints";
import { fundTypes } from "../simConfig/fundTypes";

interface OverrideParams {
  totalInvested: number;
  fundType: 'equity' | 'income' | 'other';
  repBP: number;
  uplineBP: number;
}

interface OverrideCalculationResult {
  personalCommission: number;
  overrideCommission: number;
  totalCommission: number;
  cdrRate: number;
  cdrValue: number;
  bpSpread: number;
  debugInfo: {
    repBPUsed: number;
    uplineBPUsed: number;
    cdrApplied: number;
    formula: string;
  };
}

/**
 * Calculates override commissions based on investment amount, fund type, and business percentages
 */
export const calculateOverrides = ({
  totalInvested, 
  fundType,
  repBP,
  uplineBP
}: OverrideParams): OverrideCalculationResult => {
  // Get CDR rate based on investment amount
  let cdrRate = getCDRRate(totalInvested);
  
  // Apply cap based on fund type
  const cdrCap = fundTypes[fundType].cdrCap;
  cdrRate = Math.min(cdrRate, cdrCap);
  
  // Calculate CDR value
  const cdrValue = totalInvested * cdrRate;
  
  // Calculate personal commission
  const personalCommission = cdrValue * repBP;
  
  // Calculate BP spread
  const bpSpread = uplineBP - repBP;
  
  // Calculate override commission (only if upline BP is greater than rep BP)
  const overrideCommission = bpSpread > 0 ? cdrValue * bpSpread : 0;
  
  // Calculate total commission
  const totalCommission = personalCommission + overrideCommission;
  
  // Return calculation result with debug info
  return {
    personalCommission,
    overrideCommission,
    totalCommission,
    cdrRate,
    cdrValue,
    bpSpread,
    debugInfo: {
      repBPUsed: repBP,
      uplineBPUsed: uplineBP,
      cdrApplied: cdrRate,
      formula: `CDR (${cdrRate.toFixed(4)}) × (Broker BP (${uplineBP.toFixed(4)}) - Agent BP (${repBP.toFixed(4)})) × Volume ($${totalInvested.toLocaleString()})`
    }
  };
};

/**
 * Calculates override trails based on AUM, fund type, and business percentages
 */
export const calculateOverrideTrails = ({
  aum,
  fundType,
  repTrailRate,
  uplineTrailRate
}: {
  aum: number;
  fundType: 'equity' | 'income' | 'other';
  repTrailRate: number;
  uplineTrailRate: number;
}): {
  personalTrail: number;
  overrideTrail: number;
  totalTrail: number;
  debugInfo: {
    fundTrailRate: number;
    repRateUsed: number;
    uplineRateUsed: number;
    trailSpread: number;
    formula: string;
  };
} => {
  // Get fund trail rate
  const fundTrailRate = fundTypes[fundType].trail;
  
  // Calculate trail spread
  const trailSpread = uplineTrailRate - repTrailRate;
  
  // Calculate personal trail
  const personalTrail = aum * fundTrailRate * repTrailRate;
  
  // Calculate override trail
  const overrideTrail = trailSpread > 0 ? aum * fundTrailRate * trailSpread : 0;
  
  // Calculate total trail
  const totalTrail = personalTrail + overrideTrail;
  
  return {
    personalTrail,
    overrideTrail,
    totalTrail,
    debugInfo: {
      fundTrailRate,
      repRateUsed: repTrailRate,
      uplineRateUsed: uplineTrailRate,
      trailSpread,
      formula: `AUM ($${aum.toLocaleString()}) × Fund Trail Rate (${fundTrailRate.toFixed(4)}) × (Upline Rate (${uplineTrailRate.toFixed(4)}) - Rep Rate (${repTrailRate.toFixed(4)}))`
    }
  };
};
