/**
 * Calculate direct effort income
 * @param initialInvestment Initial investment amount
 * @param numCases Number of cases
 * @param cdrRate CDR rate (as decimal, e.g., 0.055 for 5.5%)
 * @param commissionRate Commission rate (as decimal, e.g., 0.425 for 42.5%)
 * @returns Calculated direct effort income
 */
export const calculateDirectEffortIncome = (
  initialInvestment: number,
  numCases: number,
  cdrRate: number,
  commissionRate: number
): number => {
  return initialInvestment * numCases * cdrRate * commissionRate;
};

/**
 * Calculate override income
 * @param teamInvestment Team investment amount
 * @param numTeamMembers Number of team members
 * @param avgCasesPerMember Average cases per team member
 * @param cdrRate CDR rate (as decimal)
 * @param overrideRate Override rate (as decimal)
 * @returns Calculated override income
 */
export const calculateOverrideIncome = (
  teamInvestment: number,
  numTeamMembers: number,
  avgCasesPerMember: number,
  cdrRate: number,
  overrideRate: number
): number => {
  return teamInvestment * numTeamMembers * avgCasesPerMember * cdrRate * overrideRate;
};

/**
 * Calculate recurring PAC income
 * @param pacPerCase PAC amount per case
 * @param numCases Number of cases
 * @returns Calculated recurring PAC income
 */
export const calculateRecurringPacIncome = (
  pacPerCase: number,
  numCases: number
): number => {
  return pacPerCase * numCases;
};

/**
 * Calculate trail income
 * @param aum Assets under management
 * @param trailRate Trail rate (as decimal)
 * @returns Calculated trail income
 */
export const calculateTrailIncome = (
  aum: number,
  trailRate: number
): number => {
  return aum * trailRate / 12; // Monthly trail income
};

/**
 * Calculate agency override income
 * @param agencyInvestment Agency investment amount
 * @param numAgencies Number of agencies
 * @param avgCasesPerAgency Average cases per agency
 * @param cdrRate CDR rate (as decimal)
 * @param agencyOverrideRate Agency override rate (as decimal)
 * @returns Calculated agency override income
 */
export const calculateAgencyOverrideIncome = (
  agencyInvestment: number,
  numAgencies: number,
  avgCasesPerAgency: number,
  cdrRate: number,
  agencyOverrideRate: number
): number => {
  return agencyInvestment * numAgencies * avgCasesPerAgency * cdrRate * agencyOverrideRate;
};

/**
 * Calculate compound growth over time
 * @param principal Initial principal amount
 * @param annualRate Annual growth rate (as decimal)
 * @param years Number of years
 * @param monthlyContribution Monthly contribution amount
 * @returns Future value after compound growth
 */
export const calculateCompoundGrowth = (
  principal: number,
  annualRate: number,
  years: number,
  monthlyContribution: number = 0
): number => {
  const monthlyRate = annualRate / 12;
  const numMonths = years * 12;

  if (monthlyContribution === 0) {
    // Simple compound interest without contributions
    return principal * Math.pow(1 + monthlyRate, numMonths);
  } else {
    // Compound interest with regular contributions
    return principal * Math.pow(1 + monthlyRate, numMonths) +
           monthlyContribution * ((Math.pow(1 + monthlyRate, numMonths) - 1) / monthlyRate);
  }
};

/**
 * Generate a complete scenario with all income types
 * @param inputs Input parameters for scenario generation
 * @returns Complete scenario with calculated income values
 */
export const generateScenario = (inputs: {
  initialInvestment: number;
  numCases: number;
  cdrRate: number;
  commissionRate: number;
  teamInvestment?: number;
  numTeamMembers?: number;
  avgCasesPerMember?: number;
  overrideRate?: number;
  pacPerCase?: number;
  aum?: number;
  trailRate?: number;
  agencyInvestment?: number;
  numAgencies?: number;
  avgCasesPerAgency?: number;
  agencyOverrideRate?: number;
}) => {
  // Calculate direct effort income
  const directEffort = calculateDirectEffortIncome(
    inputs.initialInvestment,
    inputs.numCases,
    inputs.cdrRate,
    inputs.commissionRate
  );

  // Calculate override income if applicable
  const override = inputs.teamInvestment && inputs.numTeamMembers &&
                  inputs.avgCasesPerMember && inputs.overrideRate
    ? calculateOverrideIncome(
        inputs.teamInvestment,
        inputs.numTeamMembers,
        inputs.avgCasesPerMember,
        inputs.cdrRate,
        inputs.overrideRate
      )
    : undefined;

  // Calculate recurring PAC income if applicable
  const recurringPac = inputs.pacPerCase && inputs.numCases
    ? calculateRecurringPacIncome(inputs.pacPerCase, inputs.numCases)
    : undefined;

  // Calculate trail income if applicable
  const trailIncome = inputs.aum && inputs.trailRate
    ? calculateTrailIncome(inputs.aum, inputs.trailRate)
    : undefined;

  // Calculate agency override income if applicable
  const agencyOverride = inputs.agencyInvestment && inputs.numAgencies &&
                        inputs.avgCasesPerAgency && inputs.agencyOverrideRate
    ? calculateAgencyOverrideIncome(
        inputs.agencyInvestment,
        inputs.numAgencies,
        inputs.avgCasesPerAgency,
        inputs.cdrRate,
        inputs.agencyOverrideRate
      )
    : undefined;

  // Calculate agency recurring PAC income if applicable
  const agencyRecurringPac = inputs.pacPerCase && inputs.numAgencies && inputs.avgCasesPerAgency
    ? calculateRecurringPacIncome(inputs.pacPerCase, inputs.numAgencies * inputs.avgCasesPerAgency)
    : undefined;

  // Calculate agency trail income if applicable
  const agencyTrail = inputs.aum && inputs.trailRate && inputs.numAgencies
    ? calculateTrailIncome(inputs.aum * 0.5 * inputs.numAgencies, inputs.trailRate)
    : undefined;

  // Calculate total monthly income
  const total = directEffort +
                (override || 0) +
                (recurringPac || 0) +
                (trailIncome || 0) +
                (agencyOverride || 0) +
                (agencyRecurringPac || 0) +
                (agencyTrail || 0);

  return {
    directEffort,
    override,
    recurringPac,
    trailIncome,
    agencyOverride,
    agencyRecurringPac,
    agencyTrail,
    total
  };
};
