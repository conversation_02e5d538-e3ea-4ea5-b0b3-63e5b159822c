/**
 * Format a number as currency
 * @param value Number to format
 * @param currency Currency code (default: USD)
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

/**
 * Format a number as a percentage
 * @param value Number to format (e.g., 0.1 for 10%)
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  }).format(value);
};

/**
 * Format a number with commas
 * @param value Number to format
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted number string
 */
export const formatNumber = (
  value: number,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale).format(value);
};

/**
 * Truncate text with ellipsis
 * @param text Text to truncate
 * @param maxLength Maximum length before truncation
 * @returns Truncated text
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};

/**
 * Format a date
 * @param date Date to format
 * @param format Format style (default: 'medium')
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date,
  format: 'short' | 'medium' | 'long' = 'medium',
  locale: string = 'en-US'
): string => {
  return new Intl.DateTimeFormat(locale, {
    dateStyle: format
  }).format(date);
};
