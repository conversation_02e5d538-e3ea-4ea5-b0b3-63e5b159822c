
import React, { useEffect } from 'react';
import CompEdgeHeader from '@/components/CompEdgeHeader';
import SimulatorForm from '@/components/simulator/SimulatorForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
// ThemeProviders are now in App.tsx
import SettingsPanel from '@/components/SettingsPanel';
import { useColorTheme } from '@/contexts/ThemeContext';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useNavigate } from 'react-router-dom';

// Main content component
const MainContent = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-financial-grey">
      <CompEdgeHeader />
      <SettingsPanel />

      <motion.main
        className="container py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="grid gap-8">
          {/* Introduction Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <Card className="bg-white">
              <CardHeader className="flex flex-row items-end justify-between pb-2 space-y-0">
                <div>
                  <Tabs defaultValue="advanced" onValueChange={(value) => {
                    if (value === "simple") navigate("/");
                  }}>
                    <TabsList className="grid w-full grid-cols-2 mb-4">
                      <TabsTrigger value="simple">Simple Income Explainer</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced Calculator</TabsTrigger>
                    </TabsList>
                  </Tabs>

                  <CardTitle
                    className="text-3xl font-display font-bold text-gray-900"
                  >
                    Advanced Income Calculator
                  </CardTitle>
                  <CardDescription
                    className="mt-2 text-gray-500"
                  >
                    Calculate potential commissions for mutual funds based on various inputs.
                    Adjust the sliders and options below to simulate different scenarios.
                  </CardDescription>
                </div>
                <motion.span
                  whileHover={{ scale: 1.05 }}
                  className="text-xs px-2 py-1 rounded-full bg-emerald-100 text-emerald-800"
                >
                  CompEdge v1.0
                </motion.span>
              </CardHeader>
            </Card>
          </motion.div>

          {/* Simulator Components */}
          <SimulatorForm />

          {/* Disclaimer */}
          <div
            className="text-xs glass p-4 rounded-xl text-gray-500"
          >
            <p>
              Disclaimer: This simulator is for educational purposes only and does not represent an official
              Primerica tool. Actual commission calculations may vary. Please consult your upline or the
              company's official documentation for precise commission calculations.
            </p>
          </div>
        </div>
      </motion.main>
    </div>
  );
};

const Index = () => {
  return <MainContent />;
};

export default Index;
