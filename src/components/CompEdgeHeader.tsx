
import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3 } from 'lucide-react';
import { useColorTheme } from '@/contexts/ThemeContext';

const CompEdgeHeader = () => {
  const MotionHeader = motion.header;
  const MotionDiv = motion.div;
  const MotionButton = motion.button;

  return (
    <MotionHeader
      className="sticky top-0 z-50 w-full backdrop-blur-xl bg-white/70 dark:bg-black/30 border-b border-border/40 shadow-sm"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <MotionDiv
            whileHover={{ rotate: [0, 5, -5, 0], scale: 1.05 }}
            transition={{ duration: 0.5 }}
          >
            <BarChart3 className="h-8 w-8 text-emerald-500" />
          </MotionDiv>
          <div>
            <h1
              className="text-2xl font-display font-bold text-gray-900"
            >
              CompEdge
            </h1>
            <p className="text-xs text-gray-500">
              Commission Simulator
            </p>
          </div>
        </div>
      </div>
    </MotionHeader>
  );
};

export default CompEdgeHeader;
