
import React from 'react';
import { Bug, Info } from 'lucide-react';
import { motion } from 'framer-motion';
import { formatCurrency, formatPercentage } from '@/utils/commissionCalculator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { fundTypes } from '@/simConfig/fundTypes';

interface InvestmentDebugInfoProps {
  totalInitialInvestment: number;
  totalContributions: number;
  cdrRate: number;
  cdrValue: number;
  fundType: 'equity' | 'income' | 'other';
  colorTheme: string;
}

const InvestmentDebugInfo: React.FC<InvestmentDebugInfoProps> = ({ 
  totalInitialInvestment, 
  totalContributions, 
  cdrRate, 
  cdrValue, 
  fundType,
  colorTheme
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className={`text-xs p-3 rounded-lg ${
        colorTheme === 'primerica' 
          ? 'bg-financial-red/5 border border-financial-red/10' 
          : 'bg-financial-green/5 border border-financial-green/10'
      }`}
    >
      <h4 className="font-semibold mb-2 flex items-center gap-1">
        <Bug size={12} /> 
        Investment Breakdown
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div>
          <span className="opacity-70">Total Initial:</span> {formatCurrency(totalInitialInvestment)}
        </div>
        <div>
          <span className="opacity-70">Total Contributions:</span> {formatCurrency(totalContributions)}
        </div>
        <div>
          <span className="opacity-70">CDR %:</span> {formatPercentage(cdrRate)} 
          <Tooltip>
            <TooltipTrigger className="ml-1">
              <Info size={10} />
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-xs">
                <p>Fund Type: {fundType}</p>
                <p>CDR Cap: {formatPercentage(fundTypes[fundType].cdrCap)}</p>
                <p>Original CDR: {formatPercentage(cdrRate)} (May be capped)</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
        <div>
          <span className="opacity-70">CDR Value:</span> {formatCurrency(cdrValue)}
        </div>
      </div>
    </motion.div>
  );
};

export default InvestmentDebugInfo;
