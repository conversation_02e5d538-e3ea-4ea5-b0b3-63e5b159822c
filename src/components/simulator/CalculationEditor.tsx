import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/utils/formatting';
import { CalculationVariable, CalculationFormula, IncomeType, CardTextProperties } from '@/hooks/useCalculations';

interface CalculationEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  incomeType: IncomeType;
  initialVariables: CalculationVariable[];
  initialFormula: CalculationFormula;
  initialTextProperties: CardTextProperties;
  onSave: (variables: CalculationVariable[], formula: CalculationFormula, textProperties: CardTextProperties) => void;
}

const CalculationEditor: React.FC<CalculationEditorProps> = ({
  open,
  onOpenChange,
  incomeType,
  initialVariables,
  initialFormula,
  initialTextProperties,
  onSave
}) => {
  const [variables, setVariables] = useState<CalculationVariable[]>(initialVariables);
  const [formula, setFormula] = useState<CalculationFormula>(initialFormula);
  const [textProperties, setTextProperties] = useState<CardTextProperties>(initialTextProperties);
  const [result, setResult] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>('variables');

  // Get title based on income type
  const getTitle = () => {
    switch (incomeType) {
      case 'directEffort':
        return 'Direct Effort Income Calculation';
      case 'recurringPac':
        return 'Recurring PAC Income Calculation';
      case 'trailIncome':
        return 'Trail Income Calculation';
      case 'override':
        return 'Override Income Calculation';
      case 'agencyOverride':
        return 'Agency Override Calculation';
      case 'agencyRecurringPac':
        return 'Agency Recurring PAC Calculation';
      case 'agencyTrail':
        return 'Agency Trail Calculation';
      default:
        return 'Income Calculation';
    }
  };

  // Update a variable
  const updateVariable = (id: string, value: number) => {
    setVariables(variables.map(v =>
      v.id === id ? { ...v, value } : v
    ));
  };

  // Update formula
  const updateFormula = (newFormula: string) => {
    setFormula({ ...formula, formula: newFormula });
  };

  // Calculate result based on current variables and formula
  useEffect(() => {
    try {
      // Create a scope with all variables
      const scope: Record<string, number> = {};
      variables.forEach(v => {
        scope[v.id] = v.value;
      });

      // Replace variable IDs with their values in the formula
      let evalFormula = formula.formula;
      for (const varId in scope) {
        evalFormula = evalFormula.replace(new RegExp(varId, 'g'), scope[varId].toString());
      }

      // Evaluate the formula
      const calculatedResult = eval(evalFormula);
      setResult(calculatedResult);
    } catch (error) {
      console.error('Error calculating result:', error);
      setResult(0);
    }
  }, [variables, formula]);

  // Reset to initial values
  const handleReset = () => {
    setVariables(initialVariables);
    setFormula(initialFormula);
    setTextProperties(initialTextProperties);
  };

  // Save changes
  const handleSave = () => {
    onSave(variables, formula, textProperties);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto p-6" style={{ minHeight: '700px' }}>
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <p className="text-sm text-muted-foreground mt-1">Customize the calculation and display of this income source</p>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" style={{ minHeight: '500px' }}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="variables">Input Values</TabsTrigger>
            <TabsTrigger value="formula">Formula</TabsTrigger>
            <TabsTrigger value="text">Card Text</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="variables" className="space-y-4 mt-4" style={{ minHeight: '400px' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {variables.map((variable) => (
                <Card key={variable.id} className="shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex flex-col space-y-3">
                      <div>
                        <h4 className="text-base font-medium">{variable.name}</h4>
                        <p className="text-xs text-gray-500 mt-0.5">{variable.description}</p>
                      </div>
                      <div className="relative">
                        <Input
                          type="number"
                          value={variable.value}
                          onChange={(e) => updateVariable(variable.id, parseFloat(e.target.value) || 0)}
                          className={`w-full text-right py-2 ${variable.unit === '$' ? 'pl-6' : ''}`}
                          placeholder={`Enter ${variable.name.toLowerCase()}`}
                        />
                        {variable.unit === '$' && (
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        )}
                        {variable.unit === '%' && (
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="formula" className="space-y-4 mt-4" style={{ minHeight: '400px' }}>
            <div className="grid grid-cols-1 gap-4">
              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Calculation Formula</h4>
                    <p className="text-xs text-gray-500 mt-0.5">
                      {incomeType === 'directEffort' && 'Calculates monthly income from your personal sales efforts'}
                      {incomeType === 'recurringPac' && 'Calculates monthly income from recurring PAC contributions'}
                      {incomeType === 'trailIncome' && 'Calculates monthly income from assets under management'}
                      {incomeType === 'override' && 'Calculates monthly income from your team members\'s production'}
                      {incomeType === 'agencyOverride' && 'Calculates monthly income from your agency network\'s production'}
                      {incomeType === 'agencyRecurringPac' && 'Calculates monthly income from your agency network\'s PAC contributions'}
                      {incomeType === 'agencyTrail' && 'Calculates monthly income from your agency network\'s assets under management'}
                    </p>
                  </div>
                  <div className="mt-3">
                    <Label htmlFor="formula" className="block mb-2">Formula Expression</Label>
                    <Input
                      id="formula"
                      value={formula.formula}
                      onChange={(e) => updateFormula(e.target.value)}
                      className="font-mono w-full py-2"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Available Variables for Formula:</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Variables you can use in your formula expression</p>
                  </div>
                  <div className="mt-3 grid grid-cols-2 gap-4">
                    {variables.map((variable) => (
                      <div key={variable.id} className="flex justify-between items-center p-2 bg-slate-50 rounded border">
                        <code className="font-bold text-sm">{variable.id}</code>
                        <span className="text-gray-600 text-sm">{variable.name}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="text" className="space-y-4 mt-4" style={{ minHeight: '400px' }}>
            <div className="grid grid-cols-1 gap-4">
              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Card Text Properties</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Customize how this income card appears in the simulator</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Card Title</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Main heading for this income card</p>
                  </div>
                  <div className="mt-3">
                    <Input
                      id="cardTitle"
                      value={textProperties.title}
                      onChange={(e) => setTextProperties({...textProperties, title: e.target.value})}
                      className="w-full py-2"
                      placeholder="Enter card title"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Card Subtitle</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Brief description of income source</p>
                  </div>
                  <div className="mt-3">
                    <Input
                      id="cardSubtitle"
                      value={textProperties.subtitle}
                      onChange={(e) => setTextProperties({...textProperties, subtitle: e.target.value})}
                      className="w-full py-2"
                      placeholder="Enter card subtitle"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Card Description (Optional)</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Additional details shown below the amount</p>
                  </div>
                  <div className="mt-3">
                    <Input
                      id="cardDescription"
                      value={textProperties.description || ''}
                      onChange={(e) => setTextProperties({...textProperties, description: e.target.value})}
                      className="w-full py-2"
                      placeholder="Enter card description"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4 mt-4" style={{ minHeight: '400px' }}>
            <div className="grid grid-cols-1 gap-4">
              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Calculation Preview</h4>
                    <p className="text-xs text-gray-500 mt-0.5">See how your income card will appear in the simulator</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Card Appearance</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Visual preview of the income card</p>
                  </div>
                  <div className="mt-4 border rounded-md p-4 bg-white shadow-sm">
                    <h3 className="text-base font-semibold text-gray-900">{textProperties.title}</h3>
                    <p className="text-xs text-gray-500 mt-1">{textProperties.subtitle}</p>
                    <div className="text-2xl font-bold mt-4 text-gray-900">{formatCurrency(result)}</div>
                    {textProperties.description && (
                      <p className="text-xs text-gray-500 mt-2">{textProperties.description}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Input Values</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Current values used in calculation</p>
                  </div>
                  <div className="mt-3 grid grid-cols-2 gap-x-8 gap-y-2 text-sm">
                    {variables.map((variable) => (
                      <div key={variable.id} className="flex justify-between p-2 border-b">
                        <span className="font-medium">{variable.name}:</span>
                        <span className="font-semibold">
                          {variable.unit === '$' ? formatCurrency(variable.value) :
                           variable.unit === '%' ? `${variable.value}%` : variable.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Calculation Formula</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Formula used to calculate the result</p>
                  </div>
                  <div className="mt-3 p-3 bg-slate-50 rounded border font-mono text-sm">
                    {formula.formula}
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <div>
                    <h4 className="text-base font-medium">Monthly Result</h4>
                    <p className="text-xs text-gray-500 mt-0.5">Final calculated monthly income</p>
                  </div>
                  <div className="mt-3 text-2xl font-bold text-center p-2 bg-slate-50 rounded border">
                    {formatCurrency(result)}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between mt-6" style={{ marginTop: 'auto' }}>
          <Button variant="outline" onClick={handleReset} className="px-5">Reset</Button>
          <div className="space-x-3">
            <Button variant="outline" onClick={() => onOpenChange(false)} className="px-5">Cancel</Button>
            <Button onClick={handleSave} className="px-5">Save Changes</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CalculationEditor;
