
import React, { useMemo, useState } from 'react';
import { useColorTheme } from '@/contexts/ThemeContext';
import { motion, AnimatePresence } from 'framer-motion';
import ResultViewToggle, { ResultViewMode } from './ResultViewToggle';
import CardResultView from './ResultViews/CardResultView';
import TimelineResultView from './ResultViews/TimelineResultView';
import SplitResultView from './ResultViews/SplitResultView';
import ResultsCard from './layout/ResultsCard';
import InvestmentDebugInfo from './debug/InvestmentDebugInfo';
import TrailIncomeSection from './charts/TrailIncomeSection';

interface SimulatorResultsProps {
  results: {
    totalInitialInvestment: number;
    totalContributions: number;
    totalInvested: number;
    cdrRate: number;
    cdrValue: number;
    personalCommission: number;
    overrideCommission: number;
    totalCommission: number;
    rolling12MonthAvg: number;
    trailProjections?: {
      year: number;
      aum: number;
      personalTrail: number;
      overrideTrail: number;
      totalTrail: number;
      monthlyTrail?: number;
      generationOverrides?: {
        gen1: number;
        gen2: number;
        gen3: number;
        total: number;
      };
    }[];
    generationCommissions?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  };
  viewMode: 'agent' | 'broker';
  fundType: 'equity' | 'income' | 'other';
  marketGrowth: number;
  durationYears: number;
  enableMultiGen?: boolean;
  selectedTrailYear: 5 | 10 | 20;
  onTrailYearChange?: (year: 5 | 10 | 20) => void;
}

const SimulatorResults: React.FC<SimulatorResultsProps> = ({ 
  results, 
  viewMode, 
  fundType, 
  marketGrowth,
  durationYears,
  enableMultiGen,
  selectedTrailYear,
  onTrailYearChange
}) => {
  const { debugMode, colorTheme } = useColorTheme();
  const [resultViewMode, setResultViewMode] = useState<ResultViewMode>('cards');
  
  const selectedYearTrail = useMemo(() => {
    if (!results.trailProjections) return null;
    
    const exactYearTrail = results.trailProjections.find(p => p.year === selectedTrailYear);
    if (exactYearTrail) return exactYearTrail;
    
    const availableYears = results.trailProjections
      .filter(p => p.year <= durationYears)
      .sort((a, b) => Math.abs(selectedTrailYear - a.year) - Math.abs(selectedTrailYear - b.year));
    
    return availableYears.length > 0 ? availableYears[0] : results.trailProjections[results.trailProjections.length - 1];
  }, [results.trailProjections, selectedTrailYear, durationYears]);

  return (
    <div className="space-y-6">
      {/* Move ResultViewToggle outside ResultsCard */}
      <ResultViewToggle 
        viewMode={resultViewMode} 
        onViewModeChange={setResultViewMode} 
      />

      <ResultsCard
        debugMode={debugMode}
        cdrRate={results.cdrRate}
        cdrValue={results.cdrValue}
      >
        <AnimatePresence>
          {debugMode && (
            <InvestmentDebugInfo
              totalInitialInvestment={results.totalInitialInvestment}
              totalContributions={results.totalContributions}
              cdrRate={results.cdrRate}
              cdrValue={results.cdrValue}
              fundType={fundType}
              colorTheme={colorTheme}
            />
          )}
        </AnimatePresence>

        {/* Show the correct view based on selected mode */}
        <AnimatePresence mode="wait">
          {resultViewMode === 'cards' && (
            <motion.div
              key="cards-view"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CardResultView 
                results={results} 
                viewMode={viewMode} 
                fundType={fundType}
                selectedTrailYear={selectedTrailYear} 
              />
            </motion.div>
          )}
          
          {resultViewMode === 'timeline' && (
            <motion.div
              key="timeline-view"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <TimelineResultView results={results} viewMode={viewMode} />
            </motion.div>
          )}
          
          {resultViewMode === 'split' && (
            <motion.div
              key="split-view"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <SplitResultView results={results} viewMode={viewMode} />
            </motion.div>
          )}
        </AnimatePresence>

        <TrailIncomeSection
          results={results}
          viewMode={viewMode}
          enableMultiGen={enableMultiGen}
          selectedYearTrail={selectedYearTrail}
        />
      </ResultsCard>
    </div>
  );
};

export default SimulatorResults;
