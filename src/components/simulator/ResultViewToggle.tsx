
import React from 'react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { LayoutDashboard, Timer, SplitSquareVertical } from 'lucide-react';
import { useColorTheme } from '@/contexts/ThemeContext';
import { motion } from 'framer-motion';

export type ResultViewMode = 'cards' | 'timeline' | 'split';

interface ResultViewToggleProps {
  viewMode: ResultViewMode;
  onViewModeChange: (mode: ResultViewMode) => void;
}

const ResultViewToggle: React.FC<ResultViewToggleProps> = ({ 
  viewMode, 
  onViewModeChange 
}) => {
  const { colorTheme } = useColorTheme();
  
  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium mb-2">Select View Mode</h3>
      <ToggleGroup 
        type="single" 
        value={viewMode} 
        onValueChange={(value) => {
          if (value) onViewModeChange(value as ResultViewMode);
        }}
        className="w-full"
      >
        <ToggleGroupItem 
          value="cards" 
          className="flex-1 data-[state=on]:bg-opacity-10 data-[state=on]:text-opacity-100 transition-all"
          aria-label="Cards view"
        >
          <motion.span 
            className="flex items-center justify-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <LayoutDashboard size={18} />
            Cards
          </motion.span>
        </ToggleGroupItem>
        
        <ToggleGroupItem 
          value="timeline" 
          className="flex-1 data-[state=on]:bg-opacity-10 data-[state=on]:text-opacity-100 transition-all"
          aria-label="Timeline view"
        >
          <motion.span 
            className="flex items-center justify-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Timer size={18} />
            Timeline
          </motion.span>
        </ToggleGroupItem>
        
        <ToggleGroupItem 
          value="split" 
          className="flex-1 data-[state=on]:bg-opacity-10 data-[state=on]:text-opacity-100 transition-all"
          aria-label="Split view"
        >
          <motion.span 
            className="flex items-center justify-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <SplitSquareVertical size={18} />
            Split View
          </motion.span>
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
};

export default ResultViewToggle;
