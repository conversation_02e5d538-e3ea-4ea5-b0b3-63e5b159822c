
import React, { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Bug } from 'lucide-react';
import { formatCurrency, formatPercentage } from '@/utils/commissionCalculator';

interface ResultsCardProps {
  title?: string;
  debugMode?: boolean;
  cdrRate?: number;
  cdrValue?: number;
  children: ReactNode;
}

const ResultsCard: React.FC<ResultsCardProps> = ({ 
  title = "Commission Results", 
  debugMode = false,
  cdrRate,
  cdrValue,
  children 
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {debugMode && (
          <div className="flex items-center space-x-2 mt-2 text-xs text-muted-foreground">
            <Bug size={14} />
            <span>Debug Mode Active</span>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {children}
      </CardContent>
      
      {(cdrRate !== undefined && cdrValue !== undefined) && (
        <CardFooter className="text-xs text-muted-foreground border-t pt-4 flex flex-col md:flex-row items-start md:items-center gap-1 md:gap-2">
          <span>*CDR Rate: {formatPercentage(cdrRate)}</span>
          <span className="hidden md:inline">|</span>
          <span>CDR Value: {formatCurrency(cdrValue)}</span>
          <span className="hidden md:inline">—</span>
          <span>based on investment range and fund type cap</span>
        </CardFooter>
      )}
    </Card>
  );
};

export default ResultsCard;
