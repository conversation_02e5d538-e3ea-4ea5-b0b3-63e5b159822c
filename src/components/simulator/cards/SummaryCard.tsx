import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SummaryCardProps {
  title: string;
  value: string;
  subtitle?: string;
  className?: string;
  children?: React.ReactNode;
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  subtitle,
  className,
  children
}) => {
  const titleRef = useRef<HTMLHeadingElement>(null);
  const valueRef = useRef<HTMLDivElement>(null);
  const [padding, setPadding] = useState({
    top: 20,
    right: 20,
    bottom: 20,
    left: 20
  });

  // Adjust padding based on content size
  useEffect(() => {
    const calculateDynamicPadding = () => {
      const titleElement = titleRef.current;
      const valueElement = valueRef.current;

      if (titleElement && valueElement) {
        // Get the width of the text content
        const titleWidth = titleElement.scrollWidth;
        const valueWidth = valueElement.scrollWidth;

        // Calculate padding based on content width
        const maxWidth = Math.max(titleWidth, valueWidth);
        const containerWidth = titleElement.parentElement?.clientWidth || 0;

        // Adjust horizontal padding if content is close to edges
        let horizontalPadding = 16; // Default padding

        if (maxWidth > containerWidth - 32) {
          // Content is too wide, reduce padding
          horizontalPadding = Math.max(8, 16 - (maxWidth - (containerWidth - 32)) / 2);
        } else if (maxWidth < containerWidth - 64) {
          // Content has plenty of room, can increase padding
          horizontalPadding = Math.min(24, 16 + ((containerWidth - 64) - maxWidth) / 8);
        }

        setPadding({
          top: 16,
          right: horizontalPadding,
          bottom: 16,
          left: horizontalPadding
        });
      }
    };

    calculateDynamicPadding();

    // Recalculate on window resize
    window.addEventListener('resize', calculateDynamicPadding);
    return () => window.removeEventListener('resize', calculateDynamicPadding);
  }, [title, value]);

  return (
    <motion.div
      className={cn(
        "border rounded-md shadow-sm bg-white flex flex-col h-[160px] w-full",
        className
      )}
      style={{
        padding: `${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px`
      }}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ boxShadow: '0 4px 12px rgba(0,0,0,0.05)', transform: 'translateY(-2px)' }}
    >
      <div className="flex flex-col h-full">
        <h3 ref={titleRef} className="text-base font-semibold text-gray-900">{title}</h3>
        {subtitle && (
          <p className="text-xs text-gray-500 mt-1.5">{subtitle}</p>
        )}

        <div className="mt-auto">
          <div ref={valueRef} className="text-2xl font-bold mt-4 text-gray-900">{value}</div>
          {children}
          <div className="pb-1"></div> {/* Extra padding at the bottom */}
        </div>
      </div>
    </motion.div>
  );
};

export default SummaryCard;
