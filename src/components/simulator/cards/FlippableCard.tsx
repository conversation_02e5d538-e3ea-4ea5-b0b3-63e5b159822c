import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FlippableCardProps {
  frontContent: React.ReactNode;
  backContent: React.ReactNode;
  className?: string;
  onFlipChange?: (isFlipped: boolean) => void;
}

const FlippableCard: React.FC<FlippableCardProps> = ({
  frontContent,
  backContent,
  className,
  onFlipChange
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    const newFlippedState = !isFlipped;
    setIsFlipped(newFlippedState);
    if (onFlipChange) {
      onFlipChange(newFlippedState);
    }
  };

  return (
    <div 
      className={cn("relative cursor-pointer h-full", className)}
      onClick={handleFlip}
      style={{ perspective: '1000px' }}
    >
      <motion.div
        className="w-full h-full transition-all duration-500"
        style={{
          transformStyle: 'preserve-3d',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
          transition: 'transform 0.6s'
        }}
      >
        {/* Front of card */}
        <div 
          className="absolute w-full h-full"
          style={{ backfaceVisibility: 'hidden' }}
        >
          {frontContent}
        </div>
        
        {/* Back of card */}
        <div 
          className="absolute w-full h-full"
          style={{ 
            backfaceVisibility: 'hidden',
            transform: 'rotateY(180deg)'
          }}
        >
          {backContent}
        </div>
      </motion.div>
    </div>
  );
};

export default FlippableCard;
