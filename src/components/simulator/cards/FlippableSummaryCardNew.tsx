import React from 'react';
import { cn } from '@/lib/utils';
import FlipCard from './FlipCard';

interface Calculation {
  label: string;
  value: string | number;
  isResult?: boolean;
}

interface FlippableSummaryCardNewProps {
  title: string;
  value: string;
  subtitle?: string;
  className?: string;
  children?: React.ReactNode;
  calculations: Calculation[];
  borderColor: string;
}

const FlippableSummaryCardNew: React.FC<FlippableSummaryCardNewProps> = ({
  title,
  value,
  subtitle,
  className,
  children,
  calculations,
  borderColor
}) => {
  return (
    <FlipCard
      title={title}
      value={value}
      subtitle={subtitle}
      className={className}
      frontContent={children}
      calculations={calculations}
      borderColor={borderColor}
    />
  );
};

export default FlippableSummaryCardNew;
