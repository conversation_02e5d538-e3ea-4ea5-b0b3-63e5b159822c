import React from 'react';
import { motion } from 'framer-motion';
import CollapsedCard from './CollapsedCard';
import { formatCurrency } from '@/utils/formatting';

interface CollapsedCardsRowProps {
  directEffort?: number;
  override?: number;
  recurringPac?: number;
  trailIncome?: number;
  activeCardIndex: number | null;
}

const CollapsedCardsRow: React.FC<CollapsedCardsRowProps> = ({
  directEffort,
  override,
  recurringPac,
  trailIncome,
  activeCardIndex
}) => {
  if (activeCardIndex === null) return null;

  return (
    <motion.div 
      layout
      className="flex justify-center items-center space-x-4 py-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {activeCardIndex !== 0 && directEffort !== undefined && (
        <CollapsedCard
          value={formatCurrency(directEffort)}
          borderColor="border-blue-500"
        />
      )}
      
      {activeCardIndex !== 1 && override !== undefined && (
        <CollapsedCard
          value={formatCurrency(override)}
          borderColor="border-indigo-500"
        />
      )}
      
      {activeCardIndex !== 2 && recurringPac !== undefined && (
        <CollapsedCard
          value={formatCurrency(recurringPac)}
          borderColor="border-green-500"
        />
      )}
      
      {activeCardIndex !== 3 && trailIncome !== undefined && (
        <CollapsedCard
          value={formatCurrency(trailIncome)}
          borderColor="border-purple-500"
        />
      )}
    </motion.div>
  );
};

export default CollapsedCardsRow;
