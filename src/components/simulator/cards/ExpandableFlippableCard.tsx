import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { X } from 'lucide-react';

interface ExpandableFlippableCardProps {
  frontContent: React.ReactNode;
  backContent: React.ReactNode;
  title: string;
  className?: string;
  onFlipChange?: (isFlipped: boolean) => void;
  expandOnFlip?: boolean;
}

const ExpandableFlippableCard: React.FC<ExpandableFlippableCardProps> = ({
  frontContent,
  backContent,
  title,
  className,
  onFlipChange,
  expandOnFlip = true
}) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFlip = () => {
    const newFlippedState = !isFlipped;
    setIsFlipped(newFlippedState);
    
    // If expandOnFlip is true, also expand the card when flipped
    if (expandOnFlip && newFlippedState) {
      setIsExpanded(true);
    }
    
    if (onFlipChange) {
      onFlipChange(newFlippedState);
    }
  };

  const handleCloseModal = () => {
    setIsExpanded(false);
    
    // If we're closing the modal, also flip the card back
    if (isFlipped) {
      setIsFlipped(false);
      if (onFlipChange) {
        onFlipChange(false);
      }
    }
  };

  return (
    <>
      <div 
        className={cn("relative cursor-pointer h-full", className)}
        onClick={handleFlip}
        style={{ perspective: '1000px' }}
      >
        <motion.div
          className="w-full h-full transition-all duration-500"
          style={{
            transformStyle: 'preserve-3d',
            transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
            transition: 'transform 0.6s'
          }}
        >
          {/* Front of card */}
          <div 
            className="absolute w-full h-full"
            style={{ backfaceVisibility: 'hidden' }}
          >
            {frontContent}
          </div>
          
          {/* Back of card (only shown if not expanded) */}
          {!expandOnFlip && (
            <div 
              className="absolute w-full h-full"
              style={{ 
                backfaceVisibility: 'hidden',
                transform: 'rotateY(180deg)'
              }}
            >
              {backContent}
            </div>
          )}
        </motion.div>
      </div>

      {/* Modal for expanded view */}
      <AnimatePresence>
        {isExpanded && (
          <Dialog open={isExpanded} onOpenChange={handleCloseModal}>
            <DialogContent className="sm:max-w-[600px] p-0 overflow-hidden">
              <DialogHeader className="p-6 pb-2">
                <DialogTitle>{title}</DialogTitle>
                <button 
                  className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
                  onClick={handleCloseModal}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </button>
              </DialogHeader>
              <div className="p-6 pt-2">
                {backContent}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </AnimatePresence>
    </>
  );
};

export default ExpandableFlippableCard;
