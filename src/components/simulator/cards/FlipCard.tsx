import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Calculation {
  label: string;
  value: string | number;
  isResult?: boolean;
}

interface FlipCardProps {
  title: string;
  value: string;
  subtitle?: string;
  className?: string;
  frontContent?: React.ReactNode;
  calculations: Calculation[];
  borderColor: string;
}

const FlipCard: React.FC<FlipCardProps> = ({
  title,
  value,
  subtitle,
  className,
  frontContent,
  calculations,
  borderColor
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <div className={cn("flip-card-container", className)} style={{ perspective: '1000px', height: '180px' }}>
      <div 
        className="relative w-full h-full cursor-pointer" 
        style={{ 
          transformStyle: 'preserve-3d',
          transition: 'transform 0.6s',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
        }}
        onClick={handleFlip}
      >
        {/* Front of card */}
        <div 
          className={cn(
            "absolute w-full h-full backface-hidden border rounded-md shadow-sm bg-white flex flex-col",
            `border-l-4 ${borderColor}`
          )}
          style={{ 
            backfaceVisibility: 'hidden',
            padding: '16px 8px'
          }}
        >
          <div className="flex flex-col h-full">
            <h3 className="text-base font-semibold">{title}</h3>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}

            <div className="mt-auto">
              <div className="text-2xl font-bold mt-3">{value}</div>
              {frontContent}
              <div className="pb-2"></div>
            </div>
          </div>
        </div>

        {/* Back of card */}
        <div 
          className={cn(
            "absolute w-full h-full backface-hidden border rounded-md shadow-sm bg-white p-4",
            `border-l-4 ${borderColor}`
          )}
          style={{ 
            backfaceVisibility: 'hidden',
            transform: 'rotateY(180deg)',
            overflowY: 'auto'
          }}
        >
          <h3 className="text-sm font-semibold mb-3">{title} Calculation</h3>
          <div className="space-y-2 text-xs">
            {calculations.map((calc, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-gray-600 mr-2">{calc.label}:</span>
                <span className={cn(
                  "font-medium text-right",
                  calc.isResult ? "text-base font-bold" : ""
                )}>
                  {typeof calc.value === 'number'
                    ? new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                      }).format(calc.value)
                    : calc.value}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlipCard;
