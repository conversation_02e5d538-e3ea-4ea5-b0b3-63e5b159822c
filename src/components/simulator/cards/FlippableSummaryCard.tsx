import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { X, Edit } from 'lucide-react';
import SummaryCard from './SummaryCard';

interface Calculation {
  label: string;
  value: string | number;
  isResult?: boolean;
}

interface FlippableSummaryCardProps {
  title: string;
  value: string;
  subtitle?: string;
  className?: string;
  children?: React.ReactNode;
  calculations: Calculation[];
  borderColor: string;
  onEdit?: () => void;
}

const FlippableSummaryCard: React.FC<FlippableSummaryCardProps> = ({
  title,
  value,
  subtitle,
  className,
  children,
  calculations,
  borderColor,
  onEdit
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <div className="relative">
      {/* Backdrop when flipped */}
      {isFlipped && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={handleFlip}
        />
      )}

      {/* Front of card - visible when not flipped */}
      <div
        className={cn(
          "cursor-pointer transition-opacity duration-200",
          isFlipped ? "opacity-0 pointer-events-none" : "opacity-100"
        )}
        onClick={handleFlip}
      >
        <SummaryCard
          title={title}
          value={value}
          subtitle={subtitle}
          className={`border-l-4 ${borderColor} h-full`}
        >
          {children}
        </SummaryCard>
      </div>

      {/* Back of card - calculation details */}
      {isFlipped && (
        <div
          className={cn(
            "fixed z-50 bg-white rounded-lg shadow-xl",
            `border-l-4 ${borderColor}`
          )}
          style={{
            width: 'min(450px, 90vw)',
            maxHeight: '80vh',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
            overflowY: 'auto'
          }}
        >
          <div className="p-5">
            <div className="flex justify-between items-center border-b pb-3 mb-5">
              <h3 className="text-lg font-semibold">{title} Calculation</h3>
              <div className="flex gap-2">
                {onEdit && (
                  <button
                    className="text-blue-500 hover:text-blue-700 p-1.5 rounded-full hover:bg-blue-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit();
                      handleFlip();
                    }}
                    title="Edit Calculation"
                  >
                    <Edit className="h-5 w-5" />
                  </button>
                )}
                <button
                  className="text-gray-500 hover:text-gray-700 p-1.5 rounded-full hover:bg-gray-100"
                  onClick={handleFlip}
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {calculations.map((calc, index) => (
                <div
                  key={index}
                  className={cn(
                    "grid grid-cols-2 gap-6 items-center py-2",
                    calc.isResult ? "mt-5 pt-4 border-t border-gray-200" : ""
                  )}
                >
                  <div className="text-gray-700 font-medium">{calc.label}:</div>
                  <div className={cn(
                    "text-right",
                    calc.isResult ? "text-lg font-bold" : "font-medium"
                  )}>
                    {typeof calc.value === 'number'
                      ? new Intl.NumberFormat('en-US', {
                          style: 'currency',
                          currency: 'USD',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0
                        }).format(calc.value)
                      : calc.value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FlippableSummaryCard;
