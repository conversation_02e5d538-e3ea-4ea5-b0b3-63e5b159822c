
import React from 'react';
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { formatCurrency } from '@/utils/commissionCalculator';
import { overrideGenerations } from '@/simConfig/repInfo';

interface MultiGenSettingsProps {
  enableMultiGen: boolean;
  setEnableMultiGen: (enabled: boolean) => void;
  genOneAgents: number;
  setGenOneAgents: (value: number) => void;
  genTwoAgents: number;
  setGenTwoAgents: (value: number) => void;
  genThreeAgents: number;
  setGenThreeAgents: (value: number) => void;
  genOneRolloverCases: number;
  setGenOneRolloverCases: (value: number) => void;
  genTwoRolloverCases: number;
  setGenTwoRolloverCases: (value: number) => void;
  genThreeRolloverCases: number;
  setGenThreeRolloverCases: (value: number) => void;
  genOneMonthlyCases: number;
  setGenOneMonthlyCases: (value: number) => void;
  genTwoMonthlyCases: number;
  setGenTwoMonthlyCases: (value: number) => void;
  genThreeMonthlyCases: number;
  setGenThreeMonthlyCases: (value: number) => void;
  avgInitialInvestment: number;
  avgMonthlyContribution: number;
}

const MultiGenSettings: React.FC<MultiGenSettingsProps> = ({
  enableMultiGen,
  setEnableMultiGen,
  genOneAgents,
  setGenOneAgents,
  genTwoAgents,
  setGenTwoAgents,
  genThreeAgents,
  setGenThreeAgents,
  genOneRolloverCases,
  setGenOneRolloverCases,
  genTwoRolloverCases,
  setGenTwoRolloverCases,
  genThreeRolloverCases,
  setGenThreeRolloverCases,
  genOneMonthlyCases,
  setGenOneMonthlyCases,
  genTwoMonthlyCases,
  setGenTwoMonthlyCases,
  genThreeMonthlyCases,
  setGenThreeMonthlyCases,
  avgInitialInvestment,
  avgMonthlyContribution
}) => {
  // Format override percentages for display
  const formatOverrideRate = (gen: 1 | 2 | 3) => {
    return `${(overrideGenerations[gen] * 100).toFixed(1)}%`;
  };
  
  return (
    <div className="space-y-4 border-t pt-4 mt-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-medium">Multi-Generation Overrides</h3>
          <div className="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full">New</div>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm">Enable</span>
          <Switch
            checked={enableMultiGen}
            onCheckedChange={setEnableMultiGen}
          />
        </div>
      </div>
      
      {enableMultiGen && (
        <div className="space-y-6">
          {/* First Generation */}
          <div className="border-l-4 border-blue-400 pl-3 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                1st Generation Overrides
                <span className="ml-2 text-xs text-blue-600">({formatOverrideRate(1)} BP)</span>
              </h4>
            </div>
            
            {/* Number of 1st Generation Agents */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Number of 1st Gen Agents</label>
                <span className="text-xs font-medium">{genOneAgents}</span>
              </div>
              <Slider 
                value={[genOneAgents]} 
                min={0} 
                max={15} 
                step={1} 
                onValueChange={(value) => setGenOneAgents(value[0])} 
              />
            </div>
            
            {/* Average Rollover Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Rollover Cases per Agent</label>
                <span className="text-xs font-medium">{genOneRolloverCases}</span>
              </div>
              <Slider 
                value={[genOneRolloverCases]} 
                min={0} 
                max={10} 
                step={1} 
                onValueChange={(value) => setGenOneRolloverCases(value[0])} 
              />
              <div className="text-xs text-gray-500">
                Total: {genOneAgents * genOneRolloverCases} cases ({formatCurrency(genOneAgents * genOneRolloverCases * avgInitialInvestment)})
              </div>
            </div>
            
            {/* Average Monthly Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Monthly Cases per Agent</label>
                <span className="text-xs font-medium">{genOneMonthlyCases}</span>
              </div>
              <Slider 
                value={[genOneMonthlyCases]} 
                min={0} 
                max={10} 
                step={1} 
                onValueChange={(value) => setGenOneMonthlyCases(value[0])} 
              />
              <div className="text-xs text-gray-500">
                Total: {genOneAgents * genOneMonthlyCases} cases ({formatCurrency(genOneAgents * genOneMonthlyCases * avgMonthlyContribution)}/mo)
              </div>
            </div>
          </div>
          
          {/* Second Generation */}
          <div className="border-l-4 border-teal-400 pl-3 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                2nd Generation Overrides
                <span className="ml-2 text-xs text-teal-600">({formatOverrideRate(2)} BP)</span>
              </h4>
            </div>
            
            {/* Number of 2nd Generation Agents */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Number of 2nd Gen Agents</label>
                <span className="text-xs font-medium">{genTwoAgents}</span>
              </div>
              <Slider 
                value={[genTwoAgents]} 
                min={0} 
                max={20} 
                step={1} 
                onValueChange={(value) => setGenTwoAgents(value[0])} 
              />
            </div>
            
            {/* Average Rollover Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Rollover Cases per Agent</label>
                <span className="text-xs font-medium">{genTwoRolloverCases}</span>
              </div>
              <Slider 
                value={[genTwoRolloverCases]} 
                min={0} 
                max={10} 
                step={1} 
                onValueChange={(value) => setGenTwoRolloverCases(value[0])} 
              />
              <div className="text-xs text-gray-500">
                Total: {genTwoAgents * genTwoRolloverCases} cases ({formatCurrency(genTwoAgents * genTwoRolloverCases * avgInitialInvestment)})
              </div>
            </div>
            
            {/* Average Monthly Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Monthly Cases per Agent</label>
                <span className="text-xs font-medium">{genTwoMonthlyCases}</span>
              </div>
              <Slider 
                value={[genTwoMonthlyCases]} 
                min={0} 
                max={10} 
                step={1} 
                onValueChange={(value) => setGenTwoMonthlyCases(value[0])} 
              />
              <div className="text-xs text-gray-500">
                Total: {genTwoAgents * genTwoMonthlyCases} cases ({formatCurrency(genTwoAgents * genTwoMonthlyCases * avgMonthlyContribution)}/mo)
              </div>
            </div>
          </div>
          
          {/* Third Generation */}
          <div className="border-l-4 border-purple-400 pl-3 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                3rd Generation Overrides
                <span className="ml-2 text-xs text-purple-600">({formatOverrideRate(3)} BP)</span>
              </h4>
            </div>
            
            {/* Number of 3rd Generation Agents */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Number of 3rd Gen Agents</label>
                <span className="text-xs font-medium">{genThreeAgents}</span>
              </div>
              <Slider 
                value={[genThreeAgents]} 
                min={0} 
                max={30} 
                step={1} 
                onValueChange={(value) => setGenThreeAgents(value[0])} 
              />
            </div>
            
            {/* Average Rollover Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Rollover Cases per Agent</label>
                <span className="text-xs font-medium">{genThreeRolloverCases}</span>
              </div>
              <Slider 
                value={[genThreeRolloverCases]} 
                min={0} 
                max={10} 
                step={1} 
                onValueChange={(value) => setGenThreeRolloverCases(value[0])} 
              />
              <div className="text-xs text-gray-500">
                Total: {genThreeAgents * genThreeRolloverCases} cases ({formatCurrency(genThreeAgents * genThreeRolloverCases * avgInitialInvestment)})
              </div>
            </div>
            
            {/* Average Monthly Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Monthly Cases per Agent</label>
                <span className="text-xs font-medium">{genThreeMonthlyCases}</span>
              </div>
              <Slider 
                value={[genThreeMonthlyCases]} 
                min={0} 
                max={10} 
                step={1} 
                onValueChange={(value) => setGenThreeMonthlyCases(value[0])} 
              />
              <div className="text-xs text-gray-500">
                Total: {genThreeAgents * genThreeMonthlyCases} cases ({formatCurrency(genThreeAgents * genThreeMonthlyCases * avgMonthlyContribution)}/mo)
              </div>
            </div>
          </div>
          
          {/* Generation Summary */}
          <div className="p-3 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium mb-1">Multi-Generation Production Summary</h4>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div>
                <p className="text-gray-500">1st Gen Total</p>
                <p className="font-medium">{genOneAgents} agents</p>
                <p className="text-gray-600">{genOneAgents * genOneRolloverCases} rollovers</p>
                <p className="text-gray-600">{genOneAgents * genOneMonthlyCases} monthly</p>
              </div>
              <div>
                <p className="text-gray-500">2nd Gen Total</p>
                <p className="font-medium">{genTwoAgents} agents</p>
                <p className="text-gray-600">{genTwoAgents * genTwoRolloverCases} rollovers</p>
                <p className="text-gray-600">{genTwoAgents * genTwoMonthlyCases} monthly</p>
              </div>
              <div>
                <p className="text-gray-500">3rd Gen Total</p>
                <p className="font-medium">{genThreeAgents} agents</p>
                <p className="text-gray-600">{genThreeAgents * genThreeRolloverCases} rollovers</p>
                <p className="text-gray-600">{genThreeAgents * genThreeMonthlyCases} monthly</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiGenSettings;
