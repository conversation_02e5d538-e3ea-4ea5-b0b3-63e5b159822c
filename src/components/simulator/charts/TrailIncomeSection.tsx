
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import TrailIncomeChart from '../TrailIncomeChart';
import YearlyTrailReport from '../YearlyTrailReport';
import CommissionBreakdownChart from '../ResultComponents/CommissionBreakdownChart';
import TrailYearHighlight from '../ResultComponents/TrailYearHighlight';

interface TrailIncomeSectionProps {
  results: {
    personalCommission: number;
    overrideCommission: number;
    totalCommission: number;
    trailProjections?: {
      year: number;
      aum: number;
      personalTrail: number;
      overrideTrail: number;
      totalTrail: number;
      monthlyTrail?: number;
      generationOverrides?: {
        gen1: number;
        gen2: number;
        gen3: number;
        total: number;
      };
    }[];
    generationCommissions?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  };
  viewMode: 'agent' | 'broker';
  enableMultiGen?: boolean;
  selectedYearTrail: any; // Using any temporarily for brevity
}

const TrailIncomeSection: React.FC<TrailIncomeSectionProps> = ({
  results,
  viewMode,
  enableMultiGen,
  selectedYearTrail
}) => {
  if (!results.trailProjections || results.trailProjections.length === 0) {
    return null;
  }

  return (
    <>
      {selectedYearTrail && (
        <TrailYearHighlight 
          trailProjection={selectedYearTrail} 
          viewMode={viewMode} 
        />
      )}

      <div className="space-y-3">
        <h3 className="text-lg font-medium">Trail Income Projection</h3>
        <div className="h-64">
          <TrailIncomeChart 
            trailProjections={results.trailProjections} 
            viewMode={viewMode}
            enableMultiGen={enableMultiGen}
            selectedYear={selectedYearTrail?.year || 5}
          />
        </div>
      </div>

      <CommissionBreakdownChart 
        data={{
          personalCommission: results.personalCommission,
          overrideCommission: results.overrideCommission,
          generationCommissions: results.generationCommissions
        }}
        viewMode={viewMode}
        enableMultiGen={enableMultiGen}
      />

      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="yearly-report">
          <AccordionTrigger>Yearly Trail Income Report</AccordionTrigger>
          <AccordionContent>
            <YearlyTrailReport 
              trailProjections={results.trailProjections}
              viewMode={viewMode}
              enableMultiGen={enableMultiGen}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
};

export default TrailIncomeSection;
