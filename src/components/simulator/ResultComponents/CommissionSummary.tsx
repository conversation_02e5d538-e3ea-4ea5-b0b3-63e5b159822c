import React from 'react';
import { formatCurrency } from '@/utils/commissionCalculator';
interface CommissionSummaryProps {
  data: {
    personalCommission: number;
    overrideCommission: number;
    totalCommission: number;
    rolling12MonthAvg: number;
    generationCommissions?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  };
  viewMode: 'agent' | 'broker';
  enableMultiGen?: boolean;
}
const CommissionSummary: React.FC<CommissionSummaryProps> = ({
  data,
  viewMode,
  enableMultiGen
}) => {
  return <div className="space-y-2">
      <h3 className="text-lg font-medium">Income Details</h3>
      <div className="grid grid-cols-2 gap-2">
        <div className="p-3 rounded-md bg-financial-grey">
          <p className="text-sm text-muted-foreground">Direct Effort Income</p>
          <p className="text-lg font-semibold text-slate-950">{formatCurrency(data.personalCommission)}</p>
          <p className="text-sm text-muted-foreground mt-1">Monthly: {formatCurrency(data.personalCommission / 12)}</p>
        </div>
        {viewMode === 'broker' && <div className="p-3 bg-financial-grey rounded-md">
            <p className="text-sm text-muted-foreground">Brokerage Commission</p>
            <p className="text-lg font-semibold text-financial-lightBlue">{formatCurrency(data.overrideCommission)}</p>
            <p className="text-sm text-muted-foreground mt-1">Monthly: {formatCurrency(data.overrideCommission / 12)}</p>
          </div>}
        
        {/* Multi-Generation Override Commission Details */}
        {viewMode === 'broker' && enableMultiGen && data.generationCommissions && <div className="col-span-2 p-3 bg-financial-grey rounded-md">
            <p className="text-sm text-muted-foreground">Multi-Generation Override Commission</p>
            <div className="grid grid-cols-3 gap-2 mt-2">
              <div>
                <p className="text-xs text-muted-foreground">1st Generation</p>
                <p className="text-sm font-semibold text-financial-blue">{formatCurrency(data.generationCommissions.gen1)}</p>
                <p className="text-xs text-muted-foreground">Monthly: {formatCurrency(data.generationCommissions.gen1 / 12)}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">2nd Generation</p>
                <p className="text-sm font-semibold text-financial-blue">{formatCurrency(data.generationCommissions.gen2)}</p>
                <p className="text-xs text-muted-foreground">Monthly: {formatCurrency(data.generationCommissions.gen2 / 12)}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">3rd Generation</p>
                <p className="text-sm font-semibold text-financial-blue">{formatCurrency(data.generationCommissions.gen3)}</p>
                <p className="text-xs text-muted-foreground">Monthly: {formatCurrency(data.generationCommissions.gen3 / 12)}</p>
              </div>
            </div>
          </div>}
        
        <div className={`p-3 bg-financial-grey rounded-md ${viewMode === 'agent' ? 'col-span-1' : 'col-span-2'}`}>
          <p className="text-sm text-muted-foreground">Total Income</p>
          <p className="text-xl font-semibold text-slate-950">
            {formatCurrency(data.totalCommission + (data.generationCommissions?.total || 0))}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Monthly: {formatCurrency((data.totalCommission + (data.generationCommissions?.total || 0)) / 12)}
          </p>
        </div>
        
        <div className={`p-3 bg-financial-grey rounded-md ${viewMode === 'agent' ? 'col-span-2' : 'col-span-2'}`}>
          <p className="text-sm text-muted-foreground">Rolling 12-Month Avg</p>
          <p className="text-xl font-semibold text-financial-lightGreen">{formatCurrency(data.rolling12MonthAvg)}</p>
          <p className="text-sm text-muted-foreground mt-1">Monthly: {formatCurrency(data.rolling12MonthAvg / 12)}</p>
        </div>
      </div>
    </div>;
};
export default CommissionSummary;