
import React from 'react';
import { formatCurrency } from '@/utils/commissionCalculator';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface CommissionBreakdownChartProps {
  data: {
    personalCommission: number;
    overrideCommission: number;
    generationCommissions?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  };
  viewMode: 'agent' | 'broker';
  enableMultiGen?: boolean;
}

const CommissionBreakdownChart: React.FC<CommissionBreakdownChartProps> = ({ 
  data, 
  viewMode, 
  enableMultiGen 
}) => {
  // Create data for the commission chart
  const commissionData = [
    { name: 'Direct', value: data.personalCommission },
    ...(viewMode === 'broker' ? [{ name: 'Override', value: data.overrideCommission }] : []),
    ...(viewMode === 'broker' && enableMultiGen && data.generationCommissions ? [
      { name: 'Gen 1', value: data.generationCommissions.gen1 },
      { name: 'Gen 2', value: data.generationCommissions.gen2 },
      { name: 'Gen 3', value: data.generationCommissions.gen3 }
    ] : [])
  ];

  return (
    <div className="h-64 mt-6">
      <h3 className="text-lg font-medium mb-2">Commission Breakdown</h3>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={commissionData} layout="vertical" margin={{ top: 5, right: 30, left: 40, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" 
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} 
          />
          <YAxis dataKey="name" type="category" />
          <Tooltip 
            formatter={(value) => [`${formatCurrency(value as number)}`, 'Commission']}
            labelFormatter={(value) => `${value} Commission`}
          />
          <Bar 
            dataKey="value" 
            fill="#0062cc" 
            name="Commission"
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default CommissionBreakdownChart;
