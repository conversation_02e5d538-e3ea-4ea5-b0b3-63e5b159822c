import React from 'react';
import { formatCurrency } from '@/utils/commissionCalculator';
import { motion } from 'framer-motion';
interface InvestmentSummaryProps {
  data: {
    totalInitialInvestment: number;
    totalContributions: number;
    totalInvested: number;
  };
}
const InvestmentSummary: React.FC<InvestmentSummaryProps> = ({
  data
}) => {
  return <div className="space-y-4">
      <h3 className="text-xl font-display font-medium mb-2">Investment Summary</h3>
      <div className="grid grid-cols-2 gap-4">
        <motion.div initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        duration: 0.3
      }} className="p-4 glass rounded-2xl border border-white/20 backdrop-blur-md shadow-lg bg-slate-50">
          <p className="text-sm text-muted-foreground mb-1">Initial Investment</p>
          <motion.p className="text-xl font-display font-semibold text-financial-blue dark:text-white" initial={{
          opacity: 0.5,
          scale: 0.95
        }} animate={{
          opacity: 1,
          scale: 1
        }} transition={{
          duration: 0.3
        }}>
            {formatCurrency(data.totalInitialInvestment)}
          </motion.p>
        </motion.div>
        
        <motion.div initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        duration: 0.3,
        delay: 0.1
      }} className="p-4 glass rounded-2xl border border-white/20 backdrop-blur-md shadow-lg bg-slate-50">
          <p className="text-sm text-muted-foreground mb-1">Total Contributions</p>
          <motion.p className="text-xl font-display font-semibold text-financial-blue dark:text-white" initial={{
          opacity: 0.5,
          scale: 0.95
        }} animate={{
          opacity: 1,
          scale: 1
        }} transition={{
          duration: 0.3,
          delay: 0.1
        }}>
            {formatCurrency(data.totalContributions)}
          </motion.p>
        </motion.div>
        
        <motion.div initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        duration: 0.3,
        delay: 0.2
      }} className="p-4 glass rounded-2xl border border-white/20 backdrop-blur-md shadow-lg col-span-2 bg-financial-lightGreen">
          <p className="text-sm mb-1 text-slate-950">Total Invested</p>
          <motion.p initial={{
          opacity: 0.5,
          scale: 0.95
        }} animate={{
          opacity: 1,
          scale: 1
        }} transition={{
          duration: 0.3,
          delay: 0.2
        }} className="text-2xl font-display font-bold text-financial-green">
            {formatCurrency(data.totalInvested)}
          </motion.p>
        </motion.div>
      </div>
    </div>;
};
export default InvestmentSummary;