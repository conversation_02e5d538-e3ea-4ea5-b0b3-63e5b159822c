
import React from 'react';
import { 
  Line<PERSON>hart, 
  <PERSON>, 
  <PERSON>Axis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { formatCurrency } from '@/utils/commissionCalculator';

interface TrailIncomeChartProps {
  trailProjections: {
    year: number;
    aum: number;
    personalTrail: number;
    overrideTrail: number;
    totalTrail: number;
    generationOverrides?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  }[];
  viewMode: 'agent' | 'broker';
  enableMultiGen?: boolean;
  selectedYear: number;
}

const TrailIncomeChart: React.FC<TrailIncomeChartProps> = ({ 
  trailProjections, 
  viewMode, 
  enableMultiGen,
  selectedYear
}) => {
  // Format chart data
  const chartData = trailProjections.map(projection => ({
    year: projection.year,
    aum: projection.aum,
    personalTrail: projection.personalTrail,
    overrideTrail: projection.overrideTrail,
    gen1: enableMultiGen && projection.generationOverrides ? projection.generationOverrides.gen1 : 0,
    gen2: enableMultiGen && projection.generationOverrides ? projection.generationOverrides.gen2 : 0,
    gen3: enableMultiGen && projection.generationOverrides ? projection.generationOverrides.gen3 : 0,
    totalTrail: projection.totalTrail
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;
    
    return (
      <div className="glass p-4 border rounded-xl shadow-lg z-10">
        <p className="font-display font-semibold mb-2 text-lg">Year {label}</p>
        <p className="text-sm mb-3 text-financial-muted">AUM: <span className="money-value">{formatCurrency(payload[0]?.payload.aum)}</span></p>
        <div className="space-y-2">
          {payload.map((entry: any, index: number) => (
            entry.value > 0 && (
              <p key={index} className="text-sm flex justify-between gap-4 items-center">
                <span className="flex items-center gap-1.5">
                  <span 
                    className="inline-block w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  ></span>
                  <span>{entry.name}:</span>
                </span>
                <span className="money-value">{formatCurrency(entry.value)}</span>
              </p>
            )
          ))}
        </div>
      </div>
    );
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData}
        margin={{ top: 10, right: 30, left: 20, bottom: 5 }}
        className="animate-fade-in"
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
        <XAxis 
          dataKey="year" 
          label={{ 
            value: 'Year', 
            position: 'insideBottomRight', 
            offset: -5,
            fill: '#94A3B8'
          }}
          tick={{ fill: '#94A3B8' }}
        />
        <YAxis 
          tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
          label={{ 
            value: 'Trail Income', 
            angle: -90, 
            position: 'insideLeft',
            fill: '#94A3B8'
          }}
          tick={{ fill: '#94A3B8' }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          wrapperStyle={{ paddingTop: 10 }}
          iconType="circle"
        />
        <ReferenceLine x={selectedYear} stroke="#f43f5e" strokeDasharray="3 3" />
        
        <Line 
          type="monotone" 
          dataKey="personalTrail" 
          name="Personal Trail" 
          stroke="#22C55E" 
          strokeWidth={3}
          dot={{ r: 4, fill: '#22C55E', strokeWidth: 0 }}
          activeDot={{ r: 6, fill: '#22C55E', stroke: '#fff', strokeWidth: 2 }}
        />
        
        {viewMode === 'broker' && (
          <Line 
            type="monotone" 
            dataKey="overrideTrail" 
            name="Direct Override" 
            stroke="#0B0F1A" 
            strokeWidth={3}
            dot={{ r: 3, fill: '#0B0F1A', strokeWidth: 0 }}
            activeDot={{ r: 5, fill: '#0B0F1A', stroke: '#fff', strokeWidth: 2 }}
          />
        )}
        
        {viewMode === 'broker' && enableMultiGen && (
          <>
            <Line 
              type="monotone" 
              dataKey="gen1" 
              name="Gen 1 Override" 
              stroke="#2563EB" 
              strokeWidth={2}
              dot={{ r: 2, fill: '#2563EB', strokeWidth: 0 }}
              activeDot={{ r: 4, fill: '#2563EB', stroke: '#fff', strokeWidth: 2 }}
            />
            <Line 
              type="monotone" 
              dataKey="gen2" 
              name="Gen 2 Override" 
              stroke="#7C3AED" 
              strokeWidth={2}
              dot={{ r: 2, fill: '#7C3AED', strokeWidth: 0 }}
              activeDot={{ r: 4, fill: '#7C3AED', stroke: '#fff', strokeWidth: 2 }}
            />
            <Line 
              type="monotone" 
              dataKey="gen3" 
              name="Gen 3 Override" 
              stroke="#C026D3" 
              strokeWidth={2}
              dot={{ r: 2, fill: '#C026D3', strokeWidth: 0 }}
              activeDot={{ r: 4, fill: '#C026D3', stroke: '#fff', strokeWidth: 2 }}
            />
          </>
        )}
      </LineChart>
    </ResponsiveContainer>
  );
};

export default TrailIncomeChart;
