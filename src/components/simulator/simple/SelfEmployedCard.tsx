import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { motion, AnimatePresence } from 'framer-motion';
import { formatCurrency } from '@/utils/commissionCalculator';
import { Briefcase, ChevronUp, ChevronDown, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';

// Import our specialized card components
import DirectEffortCard from './cards/DirectEffortCard';
import RecurringPacCard from './cards/RecurringPacCard';
import TrailIncomeCard from './cards/TrailIncomeCard';
import SimpleCardTextEditor from './SimpleCardTextEditor';
import CalculationEditor from '../CalculationEditor';
import { useCalculations, IncomeType } from '@/hooks/useCalculations';
import { useCardCustomizations } from '@/hooks/useCardCustomizations';

interface SelfEmployedCardProps {
  title: string;
  subtitle: string;
  colorTheme: string;
  incomeData: {
    directEffort?: number;
    recurringPac?: number;
    trailIncome?: number;
    total: number;
  };
  simulatorInputs?: any;
}

/**
 * Self Employed card using reusable card components
 * This maintains the exact same visual appearance as the current implementation
 */
const SelfEmployedCard: React.FC<SelfEmployedCardProps> = ({
  title,
  subtitle,
  colorTheme,
  incomeData,
  simulatorInputs
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingPrimaryCard, setEditingPrimaryCard] = useState<{
    type: string;
    title: string;
    subtitle: string;
  } | null>(null);
  const [editingCalculation, setEditingCalculation] = useState<{
    incomeType: IncomeType;
    isOpen: boolean;
  } | null>(null);
  // Use card customizations hook for persistent storage
  const {
    title: primaryCardTitle,
    subtitle: primaryCardSubtitle,
    saveCustomizations
  } = useCardCustomizations('selfEmployed', title, subtitle);

  // Initialize calculations hook with simulator inputs
  const { calculations, updateCalculation, updateFromSimulatorInputs } = useCalculations(undefined, simulatorInputs);

  // Update calculations when simulator inputs change
  useEffect(() => {
    if (simulatorInputs) {
      updateFromSimulatorInputs(simulatorInputs);
    }
  }, [simulatorInputs, updateFromSimulatorInputs]);



  return (
    <Card className="shadow-lg overflow-hidden">
      <CardHeader className="p-4 flex flex-row items-center justify-between bg-blue-500 text-white">
        <div className="flex items-center gap-2">
          <Briefcase size={20} />
          <div>
            <CardTitle className="text-lg md:text-xl">{primaryCardTitle}</CardTitle>
            <p className="text-xs md:text-sm opacity-80">{primaryCardSubtitle}</p>
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setEditingPrimaryCard({
              type: 'Primary Card',
              title: primaryCardTitle,
              subtitle: primaryCardSubtitle
            });
          }}
          className="text-white/70 hover:text-white p-2 rounded-full hover:bg-white/10 transition-colors"
          title="Edit card title and subtitle"
        >
          <Edit size={16} />
        </button>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Total Monthly Income */}
        <div
          className="flex justify-between items-center py-3 cursor-pointer bg-slate-50 px-4 rounded-md"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <h3 className="text-sm uppercase text-muted-foreground font-semibold">TOTAL MONTHLY INCOME</h3>
          <div className="flex items-center">
            <span className="text-xl font-bold mr-2">{formatCurrency(incomeData.total)}</span>
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </div>
        </div>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">The Breakdown:</h3>
                  <span className="text-xl font-bold">{formatCurrency(incomeData.total)}</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {/* Direct Effort Income */}
                  <DirectEffortCard
                    value={incomeData.directEffort || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'directEffort',
                      isOpen: true
                    })}
                  />

                  {/* Recurring PAC Income */}
                  <RecurringPacCard
                    value={incomeData.recurringPac || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'recurringPac',
                      isOpen: true
                    })}
                  />

                  {/* Trail Income */}
                  <TrailIncomeCard
                    value={incomeData.trailIncome || 0}
                    showEditButton={true}
                    onEdit={() => setEditingCalculation({
                      incomeType: 'trailIncome',
                      isOpen: true
                    })}
                  />

                  {/* Empty slot for layout completeness */}
                  <div className="hidden md:block"></div>
                </div>
              </div>

              <div className="text-center text-xs text-muted-foreground mt-4">
                Click on any card above to see calculation details
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>

      {/* Primary Card Text Editor Modal */}
      {editingPrimaryCard && (
        <SimpleCardTextEditor
          open={!!editingPrimaryCard}
          onOpenChange={(open) => !open && setEditingPrimaryCard(null)}
          title={editingPrimaryCard.title}
          subtitle={editingPrimaryCard.subtitle}
          cardType={editingPrimaryCard.type}
          onSave={(newTitle, newSubtitle) => {
            saveCustomizations(newTitle, newSubtitle);
            setEditingPrimaryCard(null);
          }}
        />
      )}

      {/* Calculation Editor Modal */}
      {editingCalculation && (
        <CalculationEditor
          open={editingCalculation.isOpen}
          onOpenChange={(open) => !open && setEditingCalculation(null)}
          incomeType={editingCalculation.incomeType}
          initialVariables={calculations[editingCalculation.incomeType].variables}
          initialFormula={calculations[editingCalculation.incomeType].formula}
          initialTextProperties={calculations[editingCalculation.incomeType].textProperties}
          onSave={(variables, formula, textProperties) => {
            updateCalculation(editingCalculation.incomeType, {
              variables,
              formula,
              textProperties
            });
            setEditingCalculation(null);
          }}
        />
      )}
    </Card>
  );
};

export default SelfEmployedCard;
