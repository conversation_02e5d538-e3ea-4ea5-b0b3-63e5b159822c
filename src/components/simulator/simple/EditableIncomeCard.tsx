import React from 'react';
import { IncomeType } from '@/hooks/useCalculations';

interface EditableIncomeCardProps {
  children: React.ReactElement;
  incomeType: IncomeType;
  cardTitle: string;
  cardSubtitle: string;
  colorClass: string;
  onEditText: (type: string, title: string, subtitle: string) => void;
  onEditCalculation: (incomeType: IncomeType) => void;
}

/**
 * Wrapper component that adds editing functionality to income cards
 */
const EditableIncomeCard: React.FC<EditableIncomeCardProps> = ({
  children,
  incomeType,
  cardTitle,
  cardSubtitle,
  colorClass,
  onEditText,
  onEditCalculation
}) => {
  // Clone the child element and add edit props
  const childWithEditProps = React.cloneElement(children, {
    showEditButton: true,
    onEdit: () => onEditText(cardTitle, cardTitle, cardSubtitle)
  });

  return (
    <div className="relative group">
      {childWithEditProps}
      <button
        onClick={() => onEditCalculation(incomeType)}
        className={`absolute top-2 right-8 opacity-0 group-hover:opacity-100 ${colorClass} text-white p-1 rounded-full hover:opacity-90 transition-all text-xs`}
        title="Edit calculation"
      >
        📊
      </button>
    </div>
  );
};

export default EditableIncomeCard;
