import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

interface SimpleCardTextEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  subtitle: string;
  cardType: string;
  dropdownLabels?: {
    label1?: { current: string; placeholder: string; };
    label2?: { current: string; placeholder: string; };
  };
  onSave: (title: string, subtitle: string, dropdownLabels?: { label1?: string; label2?: string; }) => void;
}

const SimpleCardTextEditor: React.FC<SimpleCardTextEditorProps> = ({
  open,
  onOpenChange,
  title: initialTitle,
  subtitle: initialSubtitle,
  cardType,
  dropdownLabels,
  onSave
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [subtitle, setSubtitle] = useState(initialSubtitle);
  const [label1, setLabel1] = useState(dropdownLabels?.label1?.current || '');
  const [label2, setLabel2] = useState(dropdownLabels?.label2?.current || '');

  // Update local state when props change
  useEffect(() => {
    setTitle(initialTitle);
    setSubtitle(initialSubtitle);
    setLabel1(dropdownLabels?.label1?.current || '');
    setLabel2(dropdownLabels?.label2?.current || '');
  }, [initialTitle, initialSubtitle, dropdownLabels]);

  const handleSave = () => {
    const updatedDropdownLabels = dropdownLabels ? {
      label1: label1 || dropdownLabels.label1?.current,
      label2: label2 || dropdownLabels.label2?.current
    } : undefined;
    onSave(title, subtitle, updatedDropdownLabels);
    onOpenChange(false);
  };

  const handleReset = () => {
    setTitle(initialTitle);
    setSubtitle(initialSubtitle);
    setLabel1(dropdownLabels?.label1?.current || '');
    setLabel2(dropdownLabels?.label2?.current || '');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit {cardType} Card Text</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Customize the title and subtitle for this income card
          </p>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <Card>
            <CardContent className="p-4 space-y-4">
              <div>
                <Label htmlFor="cardTitle" className="text-sm font-medium">
                  Card Title
                </Label>
                <Input
                  id="cardTitle"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="mt-1"
                  placeholder="Enter card title"
                />
              </div>

              <div>
                <Label htmlFor="cardSubtitle" className="text-sm font-medium">
                  Card Subtitle
                </Label>
                <Input
                  id="cardSubtitle"
                  value={subtitle}
                  onChange={(e) => setSubtitle(e.target.value)}
                  className="mt-1"
                  placeholder="Enter card subtitle"
                />
              </div>

              {/* Dropdown Labels Section */}
              {dropdownLabels && (
                <>
                  <hr className="my-4" />
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-700">Dropdown Labels</h4>

                    {dropdownLabels.label1 && (
                      <div>
                        <Label htmlFor="label1" className="text-sm font-medium">
                          {dropdownLabels.label1.placeholder}
                        </Label>
                        <Input
                          id="label1"
                          value={label1}
                          onChange={(e) => setLabel1(e.target.value)}
                          className="mt-1"
                          placeholder={dropdownLabels.label1.current}
                        />
                      </div>
                    )}

                    {dropdownLabels.label2 && (
                      <div>
                        <Label htmlFor="label2" className="text-sm font-medium">
                          {dropdownLabels.label2.placeholder}
                        </Label>
                        <Input
                          id="label2"
                          value={label2}
                          onChange={(e) => setLabel2(e.target.value)}
                          className="mt-1"
                          placeholder={dropdownLabels.label2.current}
                        />
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardContent className="p-4">
              <div className="text-sm font-medium text-muted-foreground mb-2">Preview:</div>
              <div className="border rounded-md p-3 bg-gray-50">
                <h4 className="font-semibold text-base">{title || 'Card Title'}</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  {subtitle || 'Card subtitle'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleReset}>
            Reset
          </Button>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SimpleCardTextEditor;
