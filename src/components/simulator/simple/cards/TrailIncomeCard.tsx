import React from 'react';
import IncomeCard from './IncomeCard';
import { TrendingUp } from 'lucide-react';

interface TrailIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
}

/**
 * A specialized card for Trail Income
 */
const TrailIncomeCard: React.FC<TrailIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton
}) => {
  return (
    <IncomeCard
      title="Trail Income"
      subtitle="From personal AUM"
      value={value}
      icon={TrendingUp}
      iconText="Assets Under Management"
      colorScheme="purple"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default TrailIncomeCard;
