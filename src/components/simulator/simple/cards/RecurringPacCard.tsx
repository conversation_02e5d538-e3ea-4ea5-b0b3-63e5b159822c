import React from 'react';
import IncomeCard from './IncomeCard';
import { Repeat } from 'lucide-react';

interface RecurringPacCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
}

/**
 * A specialized card for Recurring PAC Income
 */
const RecurringPacCard: React.FC<RecurringPacCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton
}) => {
  return (
    <IncomeCard
      title="Recurring PAC Income"
      subtitle="This month's new PAC contributions"
      value={value}
      icon={Repeat}
      iconText="Monthly PAC"
      colorScheme="green"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default RecurringPacCard;
