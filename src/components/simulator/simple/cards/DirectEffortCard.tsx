import React from 'react';
import IncomeCard from './IncomeCard';
import { Briefcase } from 'lucide-react';

interface DirectEffortCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
}

/**
 * A specialized card for Direct Effort Income
 */
const DirectEffortCard: React.FC<DirectEffortCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton
}) => {
  return (
    <IncomeCard
      title="Direct Effort Income"
      subtitle="This month's new business"
      value={value}
      icon={Briefcase}
      iconText="Personal Production"
      colorScheme="blue"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default DirectEffortCard;
