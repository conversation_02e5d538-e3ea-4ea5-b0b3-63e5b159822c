import React from 'react';
import IncomeCard from './IncomeCard';
import { Users } from 'lucide-react';

interface OverrideIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
}

/**
 * A specialized card for Override Income
 */
const OverrideIncomeCard: React.FC<OverrideIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton
}) => {
  return (
    <IncomeCard
      title="Override Income"
      subtitle="From your personal team"
      value={value}
      icon={Users}
      iconText="Team Production"
      colorScheme="indigo"
      className={className}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default OverrideIncomeCard;
