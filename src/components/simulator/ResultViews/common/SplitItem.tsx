
import React, { ReactNode } from 'react';
import { cn } from "@/lib/utils";

interface SplitItemProps {
  label: string;
  value: string | number;
  subtitle?: string;
  valueClassName?: string;
}

const SplitItem: React.FC<SplitItemProps> = ({ 
  label, 
  value, 
  subtitle,
  valueClassName 
}) => {
  return (
    <div>
      <h3 className="text-sm font-semibold uppercase text-muted-foreground">
        {label}
      </h3>
      <div className={cn("text-xl mt-1 font-semibold", valueClassName)}>
        {value}
      </div>
      {subtitle && (
        <div className="text-sm text-muted-foreground">
          {subtitle}
        </div>
      )}
    </div>
  );
};

export default SplitItem;
