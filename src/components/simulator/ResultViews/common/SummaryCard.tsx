
import React, { ReactNode } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  className?: string;
  valueClassName?: string;
  children?: ReactNode;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ 
  title, 
  value, 
  subtitle, 
  className,
  valueClassName,
  children 
}) => {
  return (
    <Card className={cn("bg-white shadow-md", className)}>
      <CardContent className="p-6">
        <div className="text-sm text-gray-500">{title}</div>
        <div className={cn("text-2xl font-bold", valueClassName)}>{value}</div>
        {subtitle && (
          <div className="text-sm text-gray-500 mt-2">{subtitle}</div>
        )}
        {children}
      </CardContent>
    </Card>
  );
};

export default SummaryCard;
