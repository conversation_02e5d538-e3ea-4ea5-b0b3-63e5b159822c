
import React, { ReactNode } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface SplitColumnProps {
  title: string;
  children: ReactNode;
}

const SplitColumn: React.FC<SplitColumnProps> = ({ title, children }) => {
  return (
    <Card className="h-full">
      <CardHeader className="pb-2 border-b">
        <CardTitle className="font-display">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 pt-4">
        {children}
      </CardContent>
    </Card>
  );
};

export default SplitColumn;
