import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface CollapsedCardProps {
  value: string;
  borderColor: string;
  className?: string;
}

const CollapsedCard: React.FC<CollapsedCardProps> = ({
  value,
  borderColor,
  className
}) => {
  return (
    <motion.div 
      layout
      className={cn(
        "border rounded-md shadow-sm bg-white flex items-center justify-center",
        `border-l-4 ${borderColor}`,
        className
      )}
      style={{ 
        height: '80px', 
        width: '120px',
        minHeight: '80px'
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <span className="text-xl font-semibold">{value}</span>
    </motion.div>
  );
};

export default CollapsedCard;
