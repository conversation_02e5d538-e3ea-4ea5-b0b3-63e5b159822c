
import React, { ReactNode } from 'react';
import { cn } from "@/lib/utils";
import { useColorTheme } from '@/contexts/ThemeContext';

interface TimelineItemProps {
  label: string;
  marker: string | number;
  title: string;
  className?: string;
  children: ReactNode;
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  label,
  marker,
  title,
  className,
  children
}) => {
  const { colorTheme } = useColorTheme();

  return (
    <div className="relative">
      <div className="absolute -left-[42px] w-8 h-8 bg-white border-2 border-gray-200 rounded-full flex items-center justify-center">
        {marker}
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-900">
          {title}
        </h3>
        <div className={cn("mt-2 p-3 rounded-lg border", className)}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default TimelineItem;
