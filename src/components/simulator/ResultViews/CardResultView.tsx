
import React from 'react';
import CommissionBreakdownChart from '@/components/simulator/ResultComponents/CommissionBreakdownChart';
import TrailYearHighlight from '@/components/simulator/ResultComponents/TrailYearHighlight';
import InvestmentSummarySection from './Card/InvestmentSummarySection';
import IncomeDetailsSection from './Card/IncomeDetailsSection';

interface CardResultViewProps {
  results: any;
  viewMode: 'agent' | 'broker';
  fundType: 'equity' | 'income' | 'other';
  selectedTrailYear?: number;
}

const CardResultView: React.FC<CardResultViewProps> = ({
  results,
  viewMode,
  fundType,
  selectedTrailYear = 5
}) => {
  // Find the selected trail year projection
  const selectedTrailProjection = results.trailProjections?.find(
    (proj: any) => proj.year === selectedTrailYear
  ) || results.trailProjections?.[0];

  return (
    <div className="space-y-6">
      {/* Investment Summary Section */}
      <InvestmentSummarySection
        totalInitialInvestment={results.totalInitialInvestment}
        totalContributions={results.totalContributions}
        totalInvested={results.totalInvested}
      />

      {/* Income Details Section */}
      <IncomeDetailsSection
        personalCommission={results.personalCommission}
        totalCommission={results.totalCommission}
        rolling12MonthAvg={results.rolling12MonthAvg}
      />

      {/* 🧾 Direct + Override Income Breakdown */}
      {results && (
        <CommissionBreakdownChart
          data={{
            personalCommission: results.personalCommission,
            overrideCommission: results.overrideCommission,
            generationCommissions: results.generationCommissions,
          }}
          viewMode={viewMode}
          enableMultiGen={true}
        />
      )}

      {/* 📈 Trail Year Projection */}
      {selectedTrailProjection && (
        <TrailYearHighlight
          trailProjection={selectedTrailProjection}
          viewMode={viewMode}
        />
      )}
    </div>
  );
};

export default CardResultView;
