
import React from 'react';
import { formatCurrency } from '@/utils/commissionCalculator';
import SummaryCard from '../common/SummaryCard';

interface IncomeDetailsSectionProps {
  personalCommission: number;
  totalCommission: number;
  rolling12MonthAvg: number;
}

const IncomeDetailsSection: React.FC<IncomeDetailsSectionProps> = ({
  personalCommission,
  totalCommission,
  rolling12MonthAvg
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <SummaryCard
        title="Initial Commission"
        value={formatCurrency(personalCommission)}
        subtitle="Month 1 income"
      />
      <SummaryCard
        title="Total First Year"
        value={formatCurrency(totalCommission)}
        subtitle="All commissions"
      />
      <SummaryCard
        title="Monthly Average"
        value={formatCurrency(rolling12MonthAvg / 12)}
        subtitle={`Annual: ${formatCurrency(rolling12MonthAvg)}`}
        valueClassName="text-financial-blue"
      />
    </div>
  );
};

export default IncomeDetailsSection;
