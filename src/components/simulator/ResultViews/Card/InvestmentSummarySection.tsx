
import React from 'react';
import { formatCurrency } from '@/utils/commissionCalculator';
import SummaryCard from '../common/SummaryCard';
import { Card } from "@/components/ui/card";

interface InvestmentSummarySectionProps {
  totalInitialInvestment: number;
  totalContributions: number;
  totalInvested: number;
}

const InvestmentSummarySection: React.FC<InvestmentSummarySectionProps> = ({
  totalInitialInvestment,
  totalContributions,
  totalInvested
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <SummaryCard
        title="Initial Investment"
        value={formatCurrency(totalInitialInvestment)}
        subtitle="One-time rollover"
      />
      <SummaryCard
        title="PAC Contributions"
        value={formatCurrency(totalContributions)}
        subtitle="Monthly contributions"
      />
      <SummaryCard
        title="Total Invested"
        value={formatCurrency(totalInvested)}
        subtitle="Principal amount"
        valueClassName="text-financial-green"
      />
    </div>
  );
};

export default InvestmentSummarySection;
