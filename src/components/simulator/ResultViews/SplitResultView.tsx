
import React from 'react';
import EarnNowSection from './Split/EarnNowSection';
import EarnLaterSection from './Split/EarnLaterSection';

interface SplitResultViewProps {
  results: any; // Using any for brevity, would be properly typed in real implementation
  viewMode: 'agent' | 'broker';
}

const SplitResultView: React.FC<SplitResultViewProps> = ({ 
  results, 
  viewMode 
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Left Column - Earn Now */}
      <EarnNowSection 
        personalCommission={results.personalCommission}
        rolling12MonthAvg={results.rolling12MonthAvg}
        totalCommission={results.totalCommission}
      />
      
      {/* Right Column - Earn Later */}
      <EarnLaterSection 
        trailProjections={results.trailProjections || []}
      />
    </div>
  );
};

export default SplitResultView;
