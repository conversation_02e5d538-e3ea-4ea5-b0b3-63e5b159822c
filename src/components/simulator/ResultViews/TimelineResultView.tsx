
import React from 'react';
import { Card } from "@/components/ui/card";
import TimelineContent from './Timeline/TimelineContent';

interface TimelineResultViewProps {
  results: any; // Using any for brevity, would be properly typed in real implementation
  viewMode: 'agent' | 'broker';
}

const TimelineResultView: React.FC<TimelineResultViewProps> = ({ 
  results, 
  viewMode
}) => {
  return (
    <Card className="p-6">
      <TimelineContent results={results} viewMode={viewMode} />
    </Card>
  );
};

export default TimelineResultView;
