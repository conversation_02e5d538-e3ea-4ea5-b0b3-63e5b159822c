
import React from 'react';
import { Bug } from 'lucide-react';
import { useColorTheme } from '@/contexts/ThemeContext';

const DebugModeToggle = () => {
  const { debugMode, setDebugMode } = useColorTheme();

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-medium flex items-center gap-2">
          <Bug size={14} />
          Debug Mode
        </h3>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="debugMode"
            checked={debugMode}
            onChange={(e) => setDebugMode(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="debugMode" className="text-sm">
            {debugMode ? "On" : "Off"}
          </label>
        </div>
      </div>
      <p className="text-xs text-gray-500">
        Show calculation breakdowns and additional developer information
      </p>
    </div>
  );
};

export default DebugModeToggle;
