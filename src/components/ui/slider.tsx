
import * as React from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"
import { motion } from "framer-motion"

import { cn } from "@/lib/utils"
import { useColorTheme } from "@/contexts/ThemeContext"

interface SliderProps extends React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> {
  showTooltip?: boolean;
  formatTooltip?: (value: number) => string;
}

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  SliderProps
>(({ className, showTooltip = false, formatTooltip, ...props }, ref) => {
  const [hoveredValue, setHoveredValue] = React.useState<number | null>(null);
  const { colorTheme } = useColorTheme();
  
  const handleMouseMove = React.useCallback((e: React.MouseEvent) => {
    const slider = e.currentTarget;
    const rect = slider.getBoundingClientRect();
    const percentage = Math.min(Math.max((e.clientX - rect.left) / rect.width, 0), 1);
    
    const min = props.min || 0;
    const max = props.max || 100;
    const step = props.step || 1;
    
    const value = min + Math.round((max - min) * percentage / step) * step;
    setHoveredValue(value);
  }, [props.min, props.max, props.step]);
  
  const handleMouseLeave = () => {
    setHoveredValue(null);
  };
  
  const displayValue = (value: number) => {
    if (formatTooltip) {
      return formatTooltip(value);
    }
    return value.toString();
  };
  
  // Get theme-specific classes
  const getThemeClasses = () => {
    if (colorTheme === 'primerica') {
      return {
        range: 'bg-financial-red shadow-red-glow',
        thumb: 'border-financial-red hover:border-financial-red hover:shadow-red-glow'
      };
    }
    return {
      range: 'bg-financial-green shadow-glow',
      thumb: 'border-financial-green hover:border-financial-green hover:shadow-glow'
    };
  };
  
  const themeClasses = getThemeClasses();
  
  return (
    <div className="relative pt-6 pb-4" onMouseMove={handleMouseMove} onMouseLeave={handleMouseLeave}>
      {showTooltip && hoveredValue !== null && (
        <motion.div 
          className={`absolute top-0 left-0 transform -translate-x-1/2 -translate-y-full px-2 py-1 rounded text-xs pointer-events-none z-10 ${
            colorTheme === 'primerica' 
              ? 'bg-financial-red text-white' 
              : 'bg-financial-blue dark:bg-financial-green text-white'
          }`}
          style={{ 
            left: `${((hoveredValue - (props.min || 0)) / ((props.max || 100) - (props.min || 0))) * 100}%` 
          }}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {displayValue(hoveredValue)}
        </motion.div>
      )}
      
      <SliderPrimitive.Root
        ref={ref}
        className={cn(
          "relative flex w-full touch-none select-none items-center",
          className
        )}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-3 w-full grow overflow-hidden rounded-full bg-secondary dark:bg-slate-700">
          <SliderPrimitive.Range className={`absolute h-full ${themeClasses.range}`} />
        </SliderPrimitive.Track>
        <SliderPrimitive.Thumb className={`block h-6 w-6 rounded-full border-2 bg-white shadow-md ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-110 ${themeClasses.thumb}`}>
          <motion.div 
            className={`w-full h-full rounded-full ${
              colorTheme === 'primerica' 
                ? 'bg-financial-red opacity-0' 
                : 'bg-financial-green opacity-0'
            }`}
            animate={{ opacity: [0, 0.2, 0] }}
            transition={{ repeat: Infinity, duration: 2 }}
          />
        </SliderPrimitive.Thumb>
      </SliderPrimitive.Root>
    </div>
  )
})
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
