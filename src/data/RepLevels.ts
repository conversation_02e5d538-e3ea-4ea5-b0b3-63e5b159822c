
// Representative business percentage (BP) levels
export interface BPLevel {
  value: number;
  label: string;
}

export const RepBPLevels: BPLevel[] = [
  { value: 0.25, label: "25%" },
  { value: 0.35, label: "35%" },
  { value: 0.50, label: "50%" },
  { value: 0.85, label: "85%" }
];

export const UplineBPLevels: BPLevel[] = [
  { value: 0.50, label: "50%" },
  { value: 0.85, label: "85%" },
  { value: 0.95, label: "95%" }
];

// Product types available for simulation
export interface ProductType {
  value: string;
  label: string;
}

export const ProductTypes: ProductType[] = [
  { value: "class_a", label: "Class A Mutual Fund" },
  { value: "529_plan", label: "529 Plan" },
  { value: "utma", label: "UTMA" },
  { value: "ira", label: "IRA" }
];
