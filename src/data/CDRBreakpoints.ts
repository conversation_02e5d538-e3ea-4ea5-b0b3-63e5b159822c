
// CDR (Commission Distribution Rate) breakpoints for mutual funds
export const CDRBreakpoints = [
  { min: 0, max: 49999, rate: 0.0545 },
  { min: 50000, max: 99999, rate: 0.0565 },
  { min: 100000, max: 249999, rate: 0.0585 },
  { min: 250000, max: 499999, rate: 0.0605 },
  { min: 500000, max: 999999, rate: 0.0625 },
  { min: 1000000, max: Infinity, rate: 0.0645 }
];

// Helper function to get CDR rate based on amount
export const getCDRRate = (amount: number): number => {
  const breakpoint = CDRBreakpoints.find(
    bp => amount >= bp.min && amount <= bp.max
  );
  return breakpoint ? breakpoint.rate : 0;
};
