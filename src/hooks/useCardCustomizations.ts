import { useState, useCallback } from 'react';

export interface CardCustomization {
  title: string;
  subtitle: string;
  dropdownLabels?: {
    [key: string]: string;
  };
}

export interface AllCustomizations {
  selfEmployed?: CardCustomization;
  selfEmployedTeam?: CardCustomization;
  businessOwner?: CardCustomization;
  branchOfficePassive?: CardCustomization;
}

const STORAGE_KEY = 'card-customizations';

export const useCardCustomizations = (
  cardKey: keyof AllCustomizations,
  defaultTitle: string,
  defaultSubtitle: string,
  defaultDropdownLabels?: { [key: string]: string }
) => {
  // Load saved customizations from localStorage
  const loadCustomizations = useCallback((): CardCustomization => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const customizations: AllCustomizations = JSON.parse(saved);
        const cardCustomization = customizations[cardKey];
        
        if (cardCustomization) {
          return {
            title: cardCustomization.title || defaultTitle,
            subtitle: cardCustomization.subtitle || defaultSubtitle,
            dropdownLabels: { ...defaultDropdownLabels, ...cardCustomization.dropdownLabels }
          };
        }
      }
    } catch (e) {
      console.warn('Failed to load card customizations:', e);
    }
    
    return {
      title: defaultTitle,
      subtitle: defaultSubtitle,
      dropdownLabels: defaultDropdownLabels || {}
    };
  }, [cardKey, defaultTitle, defaultSubtitle, defaultDropdownLabels]);

  const [customization, setCustomization] = useState<CardCustomization>(loadCustomizations);

  // Save customizations to localStorage
  const saveCustomizations = useCallback((
    title: string,
    subtitle: string,
    dropdownLabels?: { [key: string]: string }
  ) => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      const allCustomizations: AllCustomizations = saved ? JSON.parse(saved) : {};
      
      allCustomizations[cardKey] = {
        title,
        subtitle,
        dropdownLabels: dropdownLabels || customization.dropdownLabels
      };
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(allCustomizations));
      
      setCustomization({
        title,
        subtitle,
        dropdownLabels: dropdownLabels || customization.dropdownLabels
      });
    } catch (e) {
      console.warn('Failed to save card customizations:', e);
    }
  }, [cardKey, customization.dropdownLabels]);

  // Clear all customizations
  const clearCustomizations = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setCustomization({
        title: defaultTitle,
        subtitle: defaultSubtitle,
        dropdownLabels: defaultDropdownLabels || {}
      });
    } catch (e) {
      console.warn('Failed to clear card customizations:', e);
    }
  }, [defaultTitle, defaultSubtitle, defaultDropdownLabels]);

  return {
    title: customization.title,
    subtitle: customization.subtitle,
    dropdownLabels: customization.dropdownLabels || {},
    saveCustomizations,
    clearCustomizations
  };
};
