import { useState } from 'react';
import { IncomeType } from './useCalculations';

export interface SimpleCardEditState {
  isOpen: boolean;
  incomeType: IncomeType | null;
  title: string;
  subtitle: string;
}

export const useSimpleCardEditor = () => {
  const [editState, setEditState] = useState<SimpleCardEditState>({
    isOpen: false,
    incomeType: null,
    title: '',
    subtitle: ''
  });

  const openEditor = (incomeType: IncomeType, title: string, subtitle: string) => {
    setEditState({
      isOpen: true,
      incomeType,
      title,
      subtitle
    });
  };

  const closeEditor = () => {
    setEditState({
      isOpen: false,
      incomeType: null,
      title: '',
      subtitle: ''
    });
  };

  const handleSave = (title: string, subtitle: string) => {
    // For now, just close the editor
    // In the future, this could update the card text
    console.log('Saving card text:', { title, subtitle });
    closeEditor();
  };

  return {
    editState,
    openEditor,
    closeEditor,
    handleSave
  };
};
