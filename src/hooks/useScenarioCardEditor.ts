import { useState } from 'react';
import { IncomeType } from './useCalculations';

export interface CardEditState {
  type: string;
  title: string;
  subtitle: string;
}

export interface CalculationEditState {
  incomeType: IncomeType;
  isOpen: boolean;
}

export const useScenarioCardEditor = (initialTitle: string, initialSubtitle: string) => {
  const [primaryCardTitle, setPrimaryCardTitle] = useState(initialTitle);
  const [primaryCardSubtitle, setPrimaryCardSubtitle] = useState(initialSubtitle);
  const [editingCard, setEditingCard] = useState<CardEditState | null>(null);
  const [editingCalculation, setEditingCalculation] = useState<CalculationEditState | null>(null);

  const openCardEditor = (type: string, title: string, subtitle: string) => {
    setEditingCard({ type, title, subtitle });
  };

  const openCalculationEditor = (incomeType: IncomeType) => {
    setEditingCalculation({ incomeType, isOpen: true });
  };

  const closeCardEditor = () => {
    setEditingCard(null);
  };

  const closeCalculationEditor = () => {
    setEditingCalculation(null);
  };

  const savePrimaryCard = (newTitle: string, newSubtitle: string) => {
    setPrimaryCardTitle(newTitle);
    setPrimaryCardSubtitle(newSubtitle);
  };

  return {
    // Primary card state
    primaryCardTitle,
    primaryCardSubtitle,
    savePrimaryCard,
    
    // Card editing state
    editingCard,
    openCardEditor,
    closeCardEditor,
    
    // Calculation editing state
    editingCalculation,
    openCalculationEditor,
    closeCalculationEditor
  };
};
