
export interface SimpleSimulationInputs {
  initialInvestment: number;
  numRolloverCases: number;
  pacPerCase: number;
  numPacCases: number;
  projectionYears: number;
  marketGrowth: number;
  numRLs: number;
  numAgencies: number;
  numRLsPerAgency: number;
}

export interface IncomeBreakdown {
  directEffort: number;
  override?: number;
  recurringPac: number;
  trailIncome: number;
  total: number;
}

export interface AgencyOverride {
  overrideIncome: number;
  recurringPacIncome: number;
  trailIncome: number;
  total: number;
}

export interface SimpleScenarioResults {
  selfEmployed: IncomeBreakdown;
  selfEmployedTeam: IncomeBreakdown & {
    teamOverride: number;
  };
  businessOwner: {
    personalOffice: IncomeBreakdown;
    agencyOverrides: AgencyOverride;
    total: number;
  };
  passiveOwner: {
    recurringPac: number;
    trailIncome: number;
    agencyOverrides: AgencyOverride;
    total: number;
  };
  aum: {
    personal: number;
    team: number;
    agencies: number;
    total: number;
  };
}
