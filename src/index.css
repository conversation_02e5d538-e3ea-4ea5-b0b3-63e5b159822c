@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  /* 3D Card Flip Utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }
}

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 142 72% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 20% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 94%;
    --muted-foreground: 215 16% 65%;

    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 47% 11%;

    --radius: 1.25rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;

    /* WealthTech Theme Variables - Default values */
    --theme-text-primary: #0f172a;
    --theme-text-secondary: #475569;
    --theme-text-muted: #94a3b8;
    --theme-text-heading: #0f172a;
    --theme-text-accent: #00e89a;

    --theme-tile-bg: #f8fafc;
    --theme-tile-border: #cbd5e1;
    --theme-tile-shadow: rgba(0, 0, 0, 0.05);
    --theme-tile-highlight: #ccfbf1;
  }

  /* Primerica Theme Variables */
  [data-color-theme="primerica"] {
    --theme-text-primary: #1e293b;
    --theme-text-secondary: #64748b;
    --theme-text-muted: #94a3b8;
    --theme-text-heading: #1e293b;
    --theme-text-accent: #ef4444;

    --theme-tile-bg: #fef2f2;
    --theme-tile-border: #fecaca;
    --theme-tile-shadow: rgba(239, 68, 68, 0.15);
    --theme-tile-highlight: #e0f2fe;
  }

  .dark {
    --background: 222 47% 8%;
    --foreground: 210 40% 98%;

    --card: 222 47% 10%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 10%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 72% 45%;
    --primary-foreground: 222 47% 8%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 75%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 213 27% 84%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 224 76% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 91% 60%;

    /* Dark theme versions of theme editor variables */
    --theme-text-primary: #f8fafc;
    --theme-text-secondary: #cbd5e1;
    --theme-text-muted: #94a3b8;
    --theme-text-heading: #f8fafc;
    --theme-text-accent: #00e89a;

    --theme-tile-bg: #1e293b;
    --theme-tile-border: #334155;
    --theme-tile-shadow: rgba(0, 0, 0, 0.3);
    --theme-tile-highlight: #0f172a;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply text-foreground font-sans antialiased;
  }

  /* Dynamic theme styles using CSS custom properties */
  .theme-text-primary {
    color: var(--theme-text-primary);
  }

  .theme-text-secondary {
    color: var(--theme-text-secondary);
  }

  .theme-text-muted {
    color: var(--theme-text-muted);
  }

  .theme-text-heading {
    color: var(--theme-text-heading);
  }

  .theme-text-accent {
    color: var(--theme-text-accent);
  }

  .theme-tile-bg {
    background-color: var(--theme-tile-bg);
  }

  .theme-tile-border {
    border-color: var(--theme-tile-border);
  }

  .theme-tile-shadow {
    box-shadow: 0 4px 12px var(--theme-tile-shadow);
  }

  .theme-tile-highlight {
    background-color: var(--theme-tile-highlight);
  }

  /* Legacy theme-specific styles with enhanced visuals */
  [data-color-theme="primerica"] {
    --financial-accent: #d9001b;
    --financial-light-accent: #009cde;
    --financial-background: #0F111A;
    --financial-card-gradient: linear-gradient(180deg, rgba(29,53,87,0.2) 0%, rgba(230,57,70,0.1) 100%);
  }

  [data-color-theme="primerica"] body {
    @apply bg-[#0F111A];
    background-image: radial-gradient(circle at top right, rgba(0, 156, 222, 0.05) 0%, transparent 60%),
                      radial-gradient(circle at bottom left, rgba(217, 0, 27, 0.05) 0%, transparent 60%);
  }

  [data-color-theme="wealthtech"] {
    --financial-accent: #059669;
    --financial-light-accent: #4ADE80;
    --financial-background: #F1F5F9;
    --financial-card-gradient: linear-gradient(180deg, rgba(255,255,255,0.9) 0%, rgba(244,246,248,0.8) 100%);
  }

  [data-color-theme="wealthtech"] body {
    @apply bg-slate-50;
    background-image: radial-gradient(circle at top right, rgba(16, 185, 129, 0.05) 0%, transparent 60%),
                      radial-gradient(circle at bottom left, rgba(16, 185, 129, 0.08) 0%, transparent 60%);
  }

  /* Custom styles for monetary values */
  .money-value {
    @apply font-display font-semibold;
  }

  [data-color-theme="wealthtech"] .money-value {
    @apply text-financial-blue dark:text-white;
  }

  [data-color-theme="primerica"] .money-value {
    @apply text-[#0F111A] dark:text-white;
  }

  /* Enhanced amount styling with theme-specific glows */
  [data-color-theme="wealthtech"] .amount {
    @apply text-[#059669] transition-all;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  }

  [data-color-theme="wealthtech"] .amount:hover {
    @apply text-[#10B981];
    text-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  }

  [data-color-theme="primerica"] .amount {
    @apply text-financial-red transition-all;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  [data-color-theme="primerica"] .amount:hover {
    text-shadow: 0 0 8px rgba(217, 0, 27, 0.4);
  }

  [data-color-theme="primerica"] .amount.broker {
    @apply text-financial-lightRed transition-all;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  [data-color-theme="primerica"] .amount.broker:hover {
    text-shadow: 0 0 8px rgba(0, 156, 222, 0.4);
  }

  /* Enhanced text for theme names to ensure readability */
  .theme-name {
    @apply font-semibold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  [data-color-theme="primerica"] .theme-name.primerica {
    @apply text-white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  [data-color-theme="wealthtech"] .theme-name.wealthtech {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  /* Improved contrast for text on various backgrounds */
  [data-color-theme="wealthtech"] .text-financial-green {
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  }

  .light-mode-text {
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  .dark-mode-text {
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  }

  /* Custom card styles with enhanced theme-specific gradients */
  .simulator-card {
    @apply rounded-3xl transition-shadow duration-300 border;
  }

  [data-color-theme="wealthtech"] .simulator-card {
    @apply bg-white dark:bg-card shadow-card hover:shadow-card-hover border-border/40 hover:border-financial-green/30;
    background-image: var(--financial-card-gradient);
  }

  [data-color-theme="primerica"] .simulator-card {
    @apply bg-[#141620] dark:bg-[#141620] border-financial-red/20 hover:border-financial-red/30;
    box-shadow: 0 0 4px rgba(230, 57, 70, 0.1), 0 0 8px rgba(29, 53, 87, 0.1);
    transition: box-shadow 0.3s ease, border-color 0.3s ease;
  }

  [data-color-theme="primerica"] .simulator-card:hover {
    box-shadow: 0 0 8px rgba(230, 57, 70, 0.2), 0 0 16px rgba(29, 53, 87, 0.2);
  }

  /* Enhanced glassmorphism for buttons and cards */
  [data-color-theme="wealthtech"] .glass {
    @apply backdrop-blur-lg bg-white/70 dark:bg-black/30 border border-white/20 dark:border-white/10 shadow-lg;
  }

  [data-color-theme="primerica"] .glass {
    @apply backdrop-blur-lg bg-[#141620]/70 dark:bg-black/30 border border-financial-red/10 dark:border-financial-red/5;
    box-shadow: 0 0 4px rgba(230, 57, 70, 0.1), 0 0 8px rgba(29, 53, 87, 0.1);
  }

  /* Enhanced gradient tile backgrounds */
  [data-color-theme="wealthtech"] .gradient-tile {
    background: linear-gradient(to right, #10B981, #34D399);
    @apply text-white border-none;
  }

  [data-color-theme="primerica"] .gradient-tile {
    background: linear-gradient(to right, #E63946, #A01A2F);
    @apply text-white border-none;
  }

  [data-color-theme="primerica"] .gradient-tile.broker {
    background: linear-gradient(to right, #1D3557, #457B9D);
    @apply text-white border-none;
  }

  /* Animated changing values */
  .animate-value {
    @apply animate-count-up;
  }

  /* Enhanced tile layouts */
  [data-color-theme="wealthtech"] .data-tile {
    @apply p-6 rounded-2xl bg-white/50 dark:bg-card/50 border border-border/40 backdrop-blur-sm;
    transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;
  }

  [data-color-theme="wealthtech"] .data-tile:hover {
    @apply border-financial-green/40;
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.15);
    transform: translateY(-2px);
  }

  [data-color-theme="primerica"] .data-tile {
    @apply p-6 rounded-2xl bg-[#141620]/50 dark:bg-card/50 border border-financial-red/20 backdrop-blur-sm;
    transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;
  }

  [data-color-theme="primerica"] .data-tile:hover {
    @apply border-financial-red/40;
    box-shadow: 0 0 15px rgba(230, 57, 70, 0.15);
    transform: translateY(-2px);
  }

  [data-color-theme="primerica"] .data-tile.broker {
    @apply border-financial-lightRed/20;
  }

  [data-color-theme="primerica"] .data-tile.broker:hover {
    @apply border-financial-lightRed/40;
    box-shadow: 0 0 15px rgba(0, 156, 222, 0.15);
  }

  /* Enhanced custom sliders */
  .custom-slider .SliderTrack {
    @apply h-3;
  }

  [data-color-theme="wealthtech"] .custom-slider .SliderTrack {
    @apply bg-financial-grey dark:bg-slate-700;
  }

  [data-color-theme="primerica"] .custom-slider .SliderTrack {
    @apply bg-[#141620] dark:bg-slate-700;
  }

  [data-color-theme="wealthtech"] .custom-slider .SliderRange {
    @apply bg-financial-green;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  }

  [data-color-theme="primerica"] .custom-slider .SliderRange {
    @apply bg-financial-red;
    box-shadow: 0 0 8px rgba(230, 57, 70, 0.4);
  }

  [data-color-theme="wealthtech"] .custom-slider .SliderThumb {
    @apply h-6 w-6 bg-white border-2 border-financial-green shadow-md hover:scale-110 transition-transform;
  }

  [data-color-theme="primerica"] .custom-slider .SliderThumb {
    @apply h-6 w-6 bg-white border-2 border-financial-red shadow-md hover:scale-110 transition-transform;
  }

  /* Enhanced switch styles for themes */
  [data-color-theme="wealthtech"] .switch[data-state="checked"] {
    @apply bg-financial-green;
  }

  [data-color-theme="primerica"] .switch[data-state="checked"] {
    @apply bg-financial-red;
  }

  /* Flip Card Animation Styles */
  .backface-hidden {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .flip-card-container {
    perspective: 1000px;
  }

  @keyframes fadeIn {
    from { opacity: 0.7; }
    to { opacity: 1; }
  }

  /* Enhanced toggle styles for themes */
  [data-color-theme="wealthtech"] .ToggleGroup-item[data-state="on"] {
    @apply bg-financial-green text-white;
  }

  [data-color-theme="primerica"] .ToggleGroup-item[data-state="on"] {
    @apply bg-financial-red text-white;
  }

  /* Debug mode styling */
  [data-debug-mode="true"] .debug-info {
    @apply block;
  }

  [data-debug-mode="false"] .debug-info {
    @apply hidden;
  }
}

/* Animations */
@keyframes count-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced glow animations for both themes */
[data-color-theme="wealthtech"] @keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
  }
}

[data-color-theme="primerica"] @keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(217, 0, 27, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(217, 0, 27, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(217, 0, 27, 0.3);
  }
}

[data-color-theme="primerica"] @keyframes pulse-glow-blue {
  0% {
    box-shadow: 0 0 5px rgba(0, 156, 222, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 156, 222, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 156, 222, 0.3);
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

[data-color-theme="primerica"] .pulse-glow.broker {
  animation: pulse-glow-blue 2s infinite;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

[data-color-theme="wealthtech"] ::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.3);
  border-radius: 3px;
}

[data-color-theme="wealthtech"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.5);
}

[data-color-theme="primerica"] ::-webkit-scrollbar-thumb {
  background: rgba(217, 0, 27, 0.3);
  border-radius: 3px;
}

[data-color-theme="primerica"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(217, 0, 27, 0.5);
}

/* Mobile optimization */
@media (max-width: 768px) {
  .simulator-card {
    @apply rounded-2xl;
  }

  .data-tile {
    @apply p-4;
  }
}

/* Utility class for horizontal scrolling on mobile */
.scroll-container {
  @apply overflow-x-auto scrollbar-none pb-1;
  -webkit-overflow-scrolling: touch;
}
