@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap');
@import url('https://rsms.me/inter/inter.css');

/* Design tokens */
:root {
  /* Colors */
  --primary: #10b981;
  --secondary: #6366f1;
  --accent: #f59e0b;
  --background: #f8fafc;
  --card-bg: #ffffff;
  --text-primary: #0f172a;
  --text-secondary: #334155;
  --text-muted: #64748b;

  /* Card colors */
  --blue-500: #3b82f6;
  --indigo-500: #6366f1;
  --green-500: #10b981;
  --purple-500: #8b5cf6;
  --red-500: #ef4444;
  --orange-500: #f97316;

  /* Typography */
  --fluid-min-width: 320;
  --fluid-max-width: 1200;
  --fluid-screen: 100vw;
  --fluid-bp: calc((var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) /
                  (var(--fluid-max-width) - var(--fluid-min-width)));

  --fs-300: clamp(0.7rem, calc(0.7rem + 0.2 * var(--fluid-bp)), 0.9rem);
  --fs-400: clamp(0.88rem, calc(0.88rem + 0.24 * var(--fluid-bp)), 1.12rem);
  --fs-500: clamp(1.09rem, calc(1.09rem + 0.31 * var(--fluid-bp)), 1.4rem);
  --fs-600: clamp(1.37rem, calc(1.37rem + 0.43 * var(--fluid-bp)), 1.8rem);
  --fs-700: clamp(1.71rem, calc(1.71rem + 0.59 * var(--fluid-bp)), 2.3rem);

  /* Spacing */
  --space-xs: clamp(0.5rem, calc(0.5rem + 0.25 * var(--fluid-bp)), 0.75rem);
  --space-sm: clamp(0.75rem, calc(0.75rem + 0.5 * var(--fluid-bp)), 1.25rem);
  --space-md: clamp(1.25rem, calc(1.25rem + 0.75 * var(--fluid-bp)), 2rem);
  --space-lg: clamp(2rem, calc(2rem + 1 * var(--fluid-bp)), 3rem);

  /* Borders */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode overrides */
.dark {
  --primary: #34d399;
  --secondary: #818cf8;
  --accent: #fbbf24;
  --background: #0f172a;
  --card-bg: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
}

/* Base styles */
body {
  font-family: 'Inter var', sans-serif;
  color: var(--text-primary);
  background-color: var(--background);
  font-size: var(--fs-400);
  line-height: 1.5;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Space Grotesk', sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: var(--fs-700); }
h2 { font-size: var(--fs-600); }
h3 { font-size: var(--fs-500); }
h4 { font-size: var(--fs-400); }

/* Utility classes */
.card {
  background-color: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Animation classes */
.fade-in {
  animation: fadeIn var(--transition-normal) forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp var(--transition-normal) forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
