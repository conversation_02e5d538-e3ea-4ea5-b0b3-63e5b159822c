
export const baseShopCommissionRates = {
  REP: 0.30,
  SRP: 0.325,
  DIS: 0.35,
  DIV: 0.375,
  REG: 0.425,
  SRL: 0.475,
  PRL: 0.50,
  RVP: 0.62
}

export const overrideGenerations = {
  1: 0.061,
  2: 0.026,
  3: 0.017,
  4: 0.013,
  5: 0.008,
  6: 0.005
}

export const trailRates = {
  REP: 0.12,
  SRP: 0.12,
  DIS: 0.12,
  DIV: 0.18,
  REG: 0.38,
  SRL: 0.44,
  PRL: 0.49,
  RVP: 0.57
}

export const generationalTrailRates = {
  1: 0.075,
  2: 0.05,
  3: 0.0225,
  4: 0.015,
  5: 0.01,
  6: 0.0075
}

// Updated repInfo object to use REG instead of PRL for agent view
export const repInfo = {
  agent: { role: "REG", bp: baseShopCommissionRates.REG, trailRate: trailRates.REG },
  broker: { role: "RVP", bp: baseShopCommissionRates.RVP, trailRate: trailRates.RVP },
  override: (upline: number, rep: number) => upline - rep
}
