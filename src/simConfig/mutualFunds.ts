
export const mutualFundLogic = {
  cdrBreakpoints: [
    { min: 0, max: 24999, cdr: { equity: 0.05, taxableFixedIncome: 0.04, municipalBond: 0.04 } },
    { min: 25000, max: 49999, cdr: { equity: 0.045, taxableFixedIncome: 0.04, municipalBond: 0.04 } },
    { min: 50000, max: 99999, cdr: { equity: 0.04, taxableFixedIncome: 0.035, municipalBond: 0.035 } },
    { min: 100000, max: 249999, cdr: { equity: 0.03, taxableFixedIncome: 0.03, municipalBond: 0.03 } },
    { min: 250000, max: 499999, cdr: { equity: 0.025, taxableFixedIncome: 0.02, municipalBond: 0.02 } },
    { min: 500000, max: 749999, cdr: { equity: 0.018, taxableFixedIncome: 0.0175, municipalBond: 0.0175 } },
    { min: 750000, max: 999999, cdr: { equity: 0.012, taxableFixedIncome: 0.012, municipalBond: 0.012 } },
    { min: 1000000, max: Infinity, cdr: { equity: 0.01, taxableFixedIncome: 0.01, municipalBond: 0.01 } }
  ]
}
