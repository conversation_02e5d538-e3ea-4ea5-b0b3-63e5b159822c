
export const calculateProjectedAUM = (start: number, years: number, rate = 0.08): number => {
  return start * Math.pow(1 + rate, years)
}

export const calculateAnnualTrail = (aum: number, trailRate: number): number => {
  const fundTrailRate = 0.0025 // 0.25% from fund company
  return aum * fundTrailRate * trailRate
}

export const calculateOverrideTrail = (aum: number, bpSpread: number): number => {
  const fundTrailRate = 0.0025
  return aum * fundTrailRate * bpSpread
}

// Export the trailsLogic object with properly typed interfaces
export const trailsLogic = {
  calculateProjectedAUM: (start: number, years: number, rate = 0.08): number => {
    return start * Math.pow(1 + rate, years)
  },
  
  calculateAnnualTrail: ({aum, trailRate}: {aum: number, trailRate: number}): number => {
    const fundTrailRate = 0.0025 // 0.25% base trail rate
    return aum * fundTrailRate * trailRate;
  },
  
  calculateOverrideTrail: ({aum, trailRate, bpSpread}: {aum: number, trailRate: number, bpSpread: number}): number => {
    const fundTrailRate = 0.0025 // 0.25% base trail rate
    return aum * fundTrailRate * trailRate * bpSpread;
  },
  
  // Helper to calculate monthly trail income
  calculateMonthlyTrail: (annualTrail: number): number => {
    return annualTrail / 12;
  },
  
  // Calculate projected AUM with contributions over time
  calculateProjectedAUMWithContributions: (
    initialInvestment: number, 
    monthlyContribution: number, 
    years: number, 
    growthRate = 0.08
  ): number => {
    // Initial investment grows for the full period
    const grownInitialInvestment = initialInvestment * Math.pow(1 + growthRate, years);
    
    // For monthly contributions, we use the future value of an annuity formula
    // FV = PMT × ((1 + r)^n - 1) / r
    // Where: PMT = monthly payment, r = growth rate, n = number of periods
    const annualContribution = monthlyContribution * 12;
    const futureValueOfContributions = 
      annualContribution * ((Math.pow(1 + growthRate, years) - 1) / growthRate);
    
    return grownInitialInvestment + futureValueOfContributions;
  }
}
