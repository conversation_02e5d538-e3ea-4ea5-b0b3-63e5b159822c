
import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeContextType } from './theme';

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  // Debug mode state
  const [debugMode, setDebugMode] = useState<boolean>(() => {
    // Check if there's a saved debug mode preference in localStorage
    const savedDebugMode = localStorage.getItem('debugMode');
    return savedDebugMode === 'true';
  });

  // Update localStorage when debug mode changes
  useEffect(() => {
    localStorage.setItem('debugMode', debugMode.toString());
    document.documentElement.setAttribute('data-debug-mode', debugMode ? 'true' : 'false');
  }, [debugMode]);

  return (
    <ThemeContext.Provider value={{
      debugMode,
      setDebugMode
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useColorTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useColorTheme must be used within a ThemeProvider');
  }
  return context;
};
