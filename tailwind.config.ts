import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				sans: ['Inter var', 'sans-serif'],
				display: ['Space Grotesk', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				financial: {
					blue: '#0B0F1A',
					lightBlue: '#2563EB',
					green: '#059669',
					lightGreen: '#4ADE80',
					red: '#d9001b',
					lightRed: '#009cde',
					grey: '#F1F5F9',
					muted: '#94A3B8'
				},
				wealthtech: {
					text: {
						primary: 'var(--theme-text-primary, #0f172a)',
						secondary: 'var(--theme-text-secondary, #475569)',
						muted: 'var(--theme-text-muted, #94a3b8)',
						heading: 'var(--theme-text-heading, #0f172a)',
						accent: 'var(--theme-text-accent, #00e89a)',
					},
					tile: {
						bg: 'var(--theme-tile-bg, #f8fafc)',
						border: 'var(--theme-tile-border, #cbd5e1)',
						shadow: 'var(--theme-tile-shadow, rgba(0, 0, 0, 0.05))',
						highlight: 'var(--theme-tile-highlight, #ccfbf1)',
					},
				},
				primerica: {
					text: {
						primary: 'var(--theme-text-primary, #1e293b)',
						secondary: 'var(--theme-text-secondary, #64748b)',
						muted: 'var(--theme-text-muted, #94a3b8)',
						heading: 'var(--theme-text-heading, #1e293b)',
						accent: 'var(--theme-text-accent, #ef4444)',
					},
					tile: {
						bg: 'var(--theme-tile-bg, #fef2f2)',
						border: 'var(--theme-tile-border, #fecaca)',
						shadow: 'var(--theme-tile-shadow, rgba(0, 0, 0, 0.05))',
						highlight: 'var(--theme-tile-highlight, #ffe4e6)',
					},
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)',
				xl: '1.25rem',
				'2xl': '1.5rem',
				'3xl': '2rem',
			},
			boxShadow: {
				card: '0 12px 32px rgba(0,0,0,0.15)',
				'card-hover': '0 16px 48px rgba(0,0,0,0.2)',
				'glow': '0 0 15px rgba(5, 150, 105, 0.4)',
				'glow-lg': '0 0 30px rgba(5, 150, 105, 0.6)',
				'red-glow': '0 0 15px rgba(217, 0, 27, 0.4)',
				'red-glow-lg': '0 0 30px rgba(217, 0, 27, 0.6)',
				'blue-glow': '0 0 15px rgba(0, 156, 222, 0.4)',
				'blue-glow-lg': '0 0 30px rgba(0, 156, 222, 0.6)',
				'dual-glow': '0 0 10px rgba(217, 0, 27, 0.3), 0 0 20px rgba(0, 156, 222, 0.2)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				fadeIn: {
					from: { opacity: '0', transform: 'translateY(10px)' },
					to: { opacity: '1', transform: 'translateY(0)' }
				},
				countUp: {
					from: { transform: 'translateY(10px)', opacity: '0' },
					to: { transform: 'translateY(0)', opacity: '1' }
				},
				'scale-pulse': {
					'0%, 100%': { transform: 'scale(1)' },
					'50%': { transform: 'scale(1.05)' },
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fadeIn 0.5s ease-out forwards',
				'count-up': 'countUp 0.5s ease-out forwards',
				'scale-pulse': 'scale-pulse 2s ease-in-out infinite'
			}
		}
	},
	plugins: [tailwindcssAnimate],
} satisfies Config;
